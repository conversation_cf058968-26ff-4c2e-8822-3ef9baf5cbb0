<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class MakeDeliverySlotIdNullable extends Migration
{
    public function up()
    {
        // Make delivery_slot_id nullable in bookings table
        $this->forge->modifyColumn('bookings', [
            'delivery_slot_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ]
        ]);
    }

    public function down()
    {
        // Revert delivery_slot_id to not nullable
        $this->forge->modifyColumn('bookings', [
            'delivery_slot_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ]
        ]);
    }
}
