<?php

if (!function_exists('calculate_shipping_charge')) {
    /**
     * Calculate shipping charge based on order total and settings
     * 
     * @param float $orderTotal The order subtotal (before shipping)
     * @return array Array containing shipping_charge and final_total
     */
    function calculate_shipping_charge($orderTotal)
    {
        // Load settings helper if not already loaded
        helper('settings');
        
        // Get shipping settings
        $shippingEnabled = get_setting('shipping_enabled', '1') == '1';
        $shippingCharge = (float) get_setting('shipping_charge', 50);
        $freeShippingThreshold = (float) get_setting('free_shipping_threshold', 500);
        
        $finalShippingCharge = 0;
        
        if ($shippingEnabled) {
            // Check if order qualifies for free shipping
            if ($freeShippingThreshold > 0 && $orderTotal >= $freeShippingThreshold) {
                $finalShippingCharge = 0; // Free shipping
            } else {
                $finalShippingCharge = $shippingCharge;
            }
        }
        
        return [
            'subtotal' => $orderTotal,
            'shipping_charge' => $finalShippingCharge,
            'total' => $orderTotal + $finalShippingCharge,
            'free_shipping_qualified' => ($freeShippingThreshold > 0 && $orderTotal >= $freeShippingThreshold),
            'free_shipping_threshold' => $freeShippingThreshold,
            'shipping_enabled' => $shippingEnabled
        ];
    }
}

if (!function_exists('get_shipping_message')) {
    /**
     * Get shipping message for display to customers
     * 
     * @param float $orderTotal Current order total
     * @return string Shipping message
     */
    function get_shipping_message($orderTotal = 0)
    {
        helper('settings');
        
        $shippingEnabled = get_setting('shipping_enabled', '1') == '1';
        $shippingMessage = get_setting('shipping_message', 'Free shipping on orders above ₹500');
        $freeShippingThreshold = (float) get_setting('free_shipping_threshold', 500);
        $currencySymbol = get_setting('currency_symbol', '₹');
        
        if (!$shippingEnabled) {
            return 'Free shipping on all orders';
        }
        
        if ($freeShippingThreshold > 0) {
            if ($orderTotal >= $freeShippingThreshold) {
                return 'Congratulations! You qualify for free shipping';
            } else {
                $remaining = $freeShippingThreshold - $orderTotal;
                return "Add {$currencySymbol}" . number_format($remaining, 2) . " more for free shipping";
            }
        }
        
        return $shippingMessage;
    }
}

if (!function_exists('format_shipping_details')) {
    /**
     * Format shipping details for display
     * 
     * @param array $shippingData Shipping calculation data
     * @return array Formatted shipping details
     */
    function format_shipping_details($shippingData)
    {
        helper('settings');
        $currencySymbol = get_setting('currency_symbol', '₹');
        
        return [
            'subtotal_formatted' => $currencySymbol . number_format($shippingData['subtotal'], 2),
            'shipping_charge_formatted' => $shippingData['shipping_charge'] > 0 
                ? $currencySymbol . number_format($shippingData['shipping_charge'], 2) 
                : 'Free',
            'total_formatted' => $currencySymbol . number_format($shippingData['total'], 2),
            'shipping_message' => get_shipping_message($shippingData['subtotal'])
        ] + $shippingData;
    }
}
