import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useCart } from '../../utils/CartContext';
import AuthService from '../../services/auth';
import ApiService from '../../services/api';
import { calculateShippingCharge } from '../../utils/shipping';
import { COLORS, SIZES, PAYMENT_METHODS } from '../../constants/config';

export default function CheckoutScreen({ navigation }) {
  const { cartItems, getCartTotal, clearCart } = useCart();
  const [loading, setLoading] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(PAYMENT_METHODS.WALLET);
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [walletBalance, setWalletBalance] = useState(0);
  const [shippingDetails, setShippingDetails] = useState(null);
  const [shippingLoading, setShippingLoading] = useState(true);

  const user = AuthService.getCurrentUser();
  const subtotal = getCartTotal();
  const deliveryFee = shippingDetails?.shipping_charge || 0;
  const total = shippingDetails?.total || subtotal;

  // Calculate shipping details when subtotal changes
  useEffect(() => {
    const fetchShippingDetails = async () => {
      if (subtotal > 0) {
        setShippingLoading(true);
        try {
          const details = await calculateShippingCharge(subtotal);
          setShippingDetails(details);
        } catch (error) {
          console.error('Error calculating shipping:', error);
          // Fallback to basic calculation
          setShippingDetails({
            subtotal: subtotal,
            shipping_charge: 30, // Default fallback
            total: subtotal + 30,
            is_free_shipping: false,
            shipping_enabled: true
          });
        } finally {
          setShippingLoading(false);
        }
      } else {
        setShippingDetails(null);
        setShippingLoading(false);
      }
    };

    fetchShippingDetails();
  }, [subtotal]);

  // Fetch wallet balance on component mount
  useEffect(() => {
    fetchWalletBalance();
  }, []);

  React.useEffect(() => {
    if (user?.address) {
      setDeliveryAddress(user.address);
    }
    fetchWalletBalance();
  }, [user]);

  const fetchWalletBalance = async () => {
    try {
      const response = await ApiService.getWallet();
      if (response.status) {
        // Ensure balance is always a number
        const balance = parseFloat(response.data.balance) || 0;
        setWalletBalance(balance);
      }
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      setWalletBalance(0); // Set to 0 on error
    }
  };

  const handlePlaceOrder = async () => {
    if (!deliveryAddress.trim()) {
      Alert.alert('Error', 'Please enter delivery address');
      return;
    }

    if (cartItems.length === 0) {
      Alert.alert('Error', 'Cart is empty');
      return;
    }

    if (shippingLoading || !shippingDetails) {
      Alert.alert('Error', 'Please wait while we calculate shipping charges');
      return;
    }

    // Check wallet balance for wallet payment
    if (selectedPaymentMethod === PAYMENT_METHODS.WALLET && (parseFloat(walletBalance) || 0) < total) {
      Alert.alert(
        'Insufficient Balance',
        `Your wallet balance is ₹${(parseFloat(walletBalance) || 0).toFixed(2)}. You need ₹${total.toFixed(2)} to place this order.`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Recharge Wallet', onPress: () => Alert.alert('Wallet', 'Wallet recharge coming soon!') }
        ]
      );
      return;
    }

    try {
      setLoading(true);

      const orderData = {
        items: cartItems.map(item => ({
          dish_id: item.dish_id,
          quantity: item.quantity,
          price: item.dish_price,
        })),
        delivery_address: deliveryAddress,
        payment_method: selectedPaymentMethod,
        special_instructions: specialInstructions,
        subtotal: subtotal,
        delivery_fee: deliveryFee,
        total: total,
      };

      console.log('Placing order with data:', orderData);
      console.log('Shipping details:', shippingDetails);

      const response = await ApiService.placeOrder(orderData);

      if (response.status) {
        // Clear cart after successful order
        await clearCart();

        Alert.alert(
          'Order Placed!',
          'Your order has been placed successfully. You will receive a confirmation shortly.',
          [
            {
              text: 'OK',
              onPress: () => {
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Home' }],
                });
                navigation.navigate('Orders');
              },
            },
          ]
        );
      } else {
        Alert.alert('Error', response.message || 'Failed to place order');
      }
    } catch (error) {
      console.error('Error placing order:', error);
      Alert.alert('Error', 'Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderPaymentMethod = (method, label, icon) => (
    <TouchableOpacity
      style={[
        styles.paymentMethod,
        selectedPaymentMethod === method && styles.paymentMethodSelected,
        method === PAYMENT_METHODS.WALLET && (parseFloat(walletBalance) || 0) < total && styles.paymentMethodDisabled,
      ]}
      onPress={() => setSelectedPaymentMethod(method)}
      disabled={method === PAYMENT_METHODS.WALLET && (parseFloat(walletBalance) || 0) < total}
    >
      <View style={styles.paymentMethodContent}>
        <Ionicons name={icon} size={24} color={COLORS.primary} />
        <View style={styles.paymentMethodTextContainer}>
          <Text style={styles.paymentMethodText}>{label}</Text>
          {method === PAYMENT_METHODS.WALLET && (
            <Text style={[
              styles.walletBalanceText,
              (parseFloat(walletBalance) || 0) < total && styles.insufficientBalanceText
            ]}>
              Balance: ₹{(parseFloat(walletBalance) || 0).toFixed(2)}
              {(parseFloat(walletBalance) || 0) < total && ' (Insufficient)'}
            </Text>
          )}
        </View>
      </View>
      <View style={[
        styles.radioButton,
        selectedPaymentMethod === method && styles.radioButtonSelected,
      ]}>
        {selectedPaymentMethod === method && (
          <View style={styles.radioButtonInner} />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Order Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          <View style={styles.summaryCard}>
            {cartItems.map((item) => (
              <View key={item.dish_id} style={styles.orderItem}>
                <Text style={styles.orderItemName}>{item.dish_name}</Text>
                <Text style={styles.orderItemDetails}>
                  {String(item.quantity)} × ₹{item.dish_price}
                </Text>
                <Text style={styles.orderItemTotal}>
                  ₹{(parseFloat(item.dish_price) * item.quantity).toFixed(2)}
                </Text>
              </View>
            ))}

            <View style={styles.summaryDivider} />

            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal</Text>
              <Text style={styles.summaryValue}>₹{subtotal.toFixed(2)}</Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Delivery Fee</Text>
              {shippingLoading ? (
                <ActivityIndicator size="small" color={COLORS.primary} />
              ) : (
                <Text style={[styles.summaryValue, shippingDetails?.is_free_shipping && styles.freeShipping]}>
                  {shippingDetails?.is_free_shipping ? 'Free' : `₹${deliveryFee.toFixed(2)}`}
                </Text>
              )}
            </View>

            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              {shippingLoading ? (
                <ActivityIndicator size="small" color={COLORS.primary} />
              ) : (
                <Text style={styles.totalValue}>₹{total.toFixed(2)}</Text>
              )}
            </View>
          </View>
        </View>

        {/* Delivery Address */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Address</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textArea}
              placeholder="Enter your delivery address"
              placeholderTextColor={COLORS.textSecondary}
              value={deliveryAddress}
              onChangeText={setDeliveryAddress}
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        {/* Payment Method */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          <View style={styles.paymentMethods}>
            {renderPaymentMethod(PAYMENT_METHODS.WALLET, 'Wallet', 'wallet-outline')}
            {renderPaymentMethod(PAYMENT_METHODS.RAZORPAY, 'Online Payment', 'card-outline')}
            {renderPaymentMethod(PAYMENT_METHODS.COD, 'Cash on Delivery', 'cash-outline')}
          </View>
        </View>

        {/* Special Instructions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Special Instructions (Optional)</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textArea}
              placeholder="Any special instructions for your order..."
              placeholderTextColor={COLORS.textSecondary}
              value={specialInstructions}
              onChangeText={setSpecialInstructions}
              multiline
              numberOfLines={2}
            />
          </View>
        </View>
      </ScrollView>

      {/* Place Order Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.placeOrderButton, (loading || shippingLoading) && styles.placeOrderButtonDisabled]}
          onPress={handlePlaceOrder}
          disabled={loading || shippingLoading}
        >
          {loading ? (
            <ActivityIndicator color={COLORS.surface} />
          ) : (
            <>
              <Text style={styles.placeOrderButtonText}>Place Order</Text>
              <Text style={styles.placeOrderTotal}>₹{total.toFixed(2)}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: SIZES.padding,
  },
  sectionTitle: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SIZES.padding,
    paddingHorizontal: SIZES.padding,
  },
  summaryCard: {
    backgroundColor: COLORS.surface,
    marginHorizontal: SIZES.padding,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.base,
  },
  orderItemName: {
    flex: 1,
    fontSize: SIZES.callout,
    color: COLORS.text,
    fontWeight: '500',
  },
  orderItemDetails: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
    marginHorizontal: SIZES.base,
  },
  orderItemTotal: {
    fontSize: SIZES.callout,
    color: COLORS.primary,
    fontWeight: '600',
  },
  summaryDivider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginVertical: SIZES.padding,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.base,
  },
  summaryLabel: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
  },
  summaryValue: {
    fontSize: SIZES.body,
    color: COLORS.text,
    fontWeight: '500',
  },
  freeShipping: {
    color: COLORS.success,
    fontWeight: 'bold',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SIZES.base,
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  totalValue: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  inputContainer: {
    marginHorizontal: SIZES.padding,
  },
  textArea: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    fontSize: SIZES.body,
    color: COLORS.text,
    borderWidth: 1,
    borderColor: COLORS.border,
    textAlignVertical: 'top',
  },
  paymentMethods: {
    marginHorizontal: SIZES.padding,
  },
  paymentMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    marginBottom: SIZES.base,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  paymentMethodSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '10',
  },
  paymentMethodContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentMethodTextContainer: {
    flex: 1,
    marginLeft: SIZES.padding,
  },
  paymentMethodText: {
    fontSize: SIZES.body,
    color: COLORS.text,
    fontWeight: '500',
  },
  walletBalanceText: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  insufficientBalanceText: {
    color: COLORS.error,
  },
  paymentMethodDisabled: {
    opacity: 0.5,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: COLORS.primary,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primary,
  },
  footer: {
    backgroundColor: COLORS.surface,
    padding: SIZES.padding,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  placeOrderButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    paddingVertical: SIZES.padding,
    paddingHorizontal: SIZES.padding,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  placeOrderButtonDisabled: {
    opacity: 0.7,
  },
  placeOrderButtonText: {
    color: COLORS.surface,
    fontSize: SIZES.body,
    fontWeight: 'bold',
  },
  placeOrderTotal: {
    color: COLORS.surface,
    fontSize: SIZES.title3,
    fontWeight: 'bold',
  },
});
