<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class FixPassword extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'fix:password';
    protected $description = 'Fix password using direct database update';

    public function run(array $params)
    {
        $db = \Config\Database::connect();
        
        $email = $params[0] ?? '<EMAIL>';
        $newPassword = $params[1] ?? '123456789';
        
        CLI::write("Fixing password for: $email", 'yellow');
        CLI::write("New password: $newPassword", 'yellow');
        
        // Create a fresh hash
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        CLI::write("Generated hash: $hashedPassword");
        
        // Verify the hash works before storing
        $testVerify = password_verify($newPassword, $hashedPassword);
        CLI::write("Pre-store verification: " . ($testVerify ? 'PASSED' : 'FAILED'), $testVerify ? 'green' : 'red');
        
        if (!$testVerify) {
            CLI::write("Hash generation failed! Aborting.", 'red');
            return;
        }
        
        // Update directly in database
        $result = $db->query("UPDATE users SET password = ? WHERE email = ?", [$hashedPassword, $email]);
        
        if ($result) {
            CLI::write("Database update successful!", 'green');
            
            // Verify by reading back from database
            $user = $db->query("SELECT password FROM users WHERE email = ?", [$email])->getRow();
            
            if ($user) {
                CLI::write("Retrieved hash: " . $user->password);
                CLI::write("Hash match: " . ($user->password === $hashedPassword ? 'YES' : 'NO'));
                
                $finalVerify = password_verify($newPassword, $user->password);
                CLI::write("Final verification: " . ($finalVerify ? 'PASSED' : 'FAILED'), $finalVerify ? 'green' : 'red');
            }
        } else {
            CLI::write("Database update failed!", 'red');
        }
    }
}
