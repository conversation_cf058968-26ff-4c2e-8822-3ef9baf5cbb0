import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import ApiService from '../../services/api';
import { useCart } from '../../utils/CartContext';
import { safeText, formatPrice, formatRating, getImageUrl } from '../../utils/textUtils';
import Header from '../../components/Header';
import { COLORS, SIZES } from '../../constants/config';

export default function MenuScreen({ navigation }) {
  const [dishes, setDishes] = useState([]);
  const [filteredDishes, setFilteredDishes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const { addToCart, getCartItem, cartLoading } = useCart();

  useEffect(() => {
    loadDishes();
  }, []);

  useEffect(() => {
    filterDishes();
  }, [searchQuery, dishes]);

  const loadDishes = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getDishes();

      if (response.status && response.data) {
        setDishes(response.data);
        setFilteredDishes(response.data);
      } else {
        Alert.alert('Error', 'Failed to load dishes');
      }
    } catch (error) {
      console.error('Error loading dishes:', error);
      Alert.alert('Error', 'Failed to load dishes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDishes();
    setRefreshing(false);
  };

  const filterDishes = () => {
    if (!searchQuery.trim()) {
      setFilteredDishes(dishes);
    } else {
      const filtered = dishes.filter(dish =>
        dish.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dish.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDishes(filtered);
    }
  };

  const handleAddToCart = async (dish) => {
    const result = await addToCart(dish, 1);
    if (result.success) {
      Alert.alert('Success', `${dish.name} added to cart!`);
    } else {
      Alert.alert('Error', result.error || 'Failed to add item to cart');
    }
  };

  const renderDishItem = ({ item }) => {
    const cartItem = getCartItem(item.id);
    const isInCart = !!cartItem;

    return (
      <TouchableOpacity
        style={styles.dishCard}
        onPress={() => navigation.navigate('DishDetails', { dishId: item.id, dish: item })}
      >
        <Image
          source={{ uri: getImageUrl(item.image || item.image_url, 'https://via.placeholder.com/150x150') }}
          style={styles.dishImage}
          resizeMode="cover"
        />

        <View style={styles.dishInfo}>
          <Text style={styles.dishName} numberOfLines={2}>{safeText(item.name, 'Dish Name')}</Text>

          {item.description && (
            <Text style={styles.dishDescription} numberOfLines={2}>
              {safeText(item.description, 'Description')}
            </Text>
          )}

          <View style={styles.dishFooter}>
            <View style={styles.priceContainer}>
              <Text style={styles.dishPrice}>{formatPrice(item.price)}</Text>
              {item.rating && item.rating > 0 && (
                <View style={styles.ratingContainer}>
                  <Ionicons name="star" size={14} color={COLORS.warning} />
                  <Text style={styles.ratingText}>{formatRating(item.rating)}</Text>
                </View>
              )}
            </View>

            <TouchableOpacity
              style={[
                styles.addButton,
                isInCart && styles.addButtonActive,
                cartLoading && styles.addButtonDisabled
              ]}
              onPress={() => handleAddToCart(item)}
              disabled={cartLoading}
            >
              {cartLoading ? (
                <ActivityIndicator size="small" color={COLORS.surface} />
              ) : (
                <>
                  <Ionicons
                    name={isInCart ? "checkmark" : "add"}
                    size={16}
                    color={COLORS.surface}
                  />
                  <Text style={styles.addButtonText}>
                    {isInCart ? `In Cart (${safeText(String(cartItem?.quantity || 0))})` : 'Add'}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading menu...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header title="Menu" navigation={navigation} currentRoute="Menu" />
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={COLORS.textSecondary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search dishes..."
            placeholderTextColor={COLORS.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={COLORS.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Dishes List */}
      <FlatList
        data={filteredDishes}
        renderItem={renderDishItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        columnWrapperStyle={styles.dishRow}
        contentContainerStyle={styles.dishList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="restaurant-outline" size={64} color={COLORS.textSecondary} />
            <Text style={styles.emptyText}>
              {searchQuery ? 'No dishes found' : 'No dishes available'}
            </Text>
            {searchQuery && (
              <Text style={styles.emptySubtext}>
                Try searching with different keywords
              </Text>
            )}
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
    marginTop: SIZES.padding,
  },
  searchContainer: {
    paddingHorizontal: SIZES.padding,
    paddingVertical: SIZES.base,
    backgroundColor: COLORS.surface,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: SIZES.radius,
    paddingHorizontal: SIZES.padding,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  searchIcon: {
    marginRight: SIZES.base,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: SIZES.body,
    color: COLORS.text,
  },
  dishList: {
    paddingHorizontal: SIZES.padding,
    paddingBottom: SIZES.padding,
  },
  dishRow: {
    justifyContent: 'space-between',
    marginBottom: SIZES.padding,
  },
  dishCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radius,
    overflow: 'hidden',
    width: '48%',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dishImage: {
    width: '100%',
    height: 120,
  },
  dishInfo: {
    padding: SIZES.padding,
  },
  dishName: {
    fontSize: SIZES.callout,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  dishDescription: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
    marginBottom: SIZES.base,
  },
  dishFooter: {
    marginTop: SIZES.base,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.base,
  },
  dishPrice: {
    fontSize: SIZES.body,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
    marginLeft: 2,
  },
  addButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius / 2,
    paddingVertical: SIZES.base,
    paddingHorizontal: SIZES.padding,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  addButtonActive: {
    backgroundColor: COLORS.success,
  },
  addButtonDisabled: {
    opacity: 0.7,
  },
  addButtonText: {
    color: COLORS.surface,
    fontSize: SIZES.caption1,
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SIZES.padding * 4,
  },
  emptyText: {
    fontSize: SIZES.title3,
    color: COLORS.textSecondary,
    marginTop: SIZES.padding,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: SIZES.body,
    color: COLORS.textLight,
    marginTop: SIZES.base,
    textAlign: 'center',
  },
});
