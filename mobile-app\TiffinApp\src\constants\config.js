import Constants from 'expo-constants';

// API Configuration
// For development, use your computer's IP address (not localhost)
// Replace *********** with your actual computer's IP address
const isDevelopment = __DEV__ || process.env.NODE_ENV === 'development';
export const API_BASE_URL = isDevelopment
  ? 'http://***********/tiffine/public/index.php/api'  // Use your computer's IP
  : (Constants.expoConfig?.extra?.apiBaseUrl || 'https://boxbites.in/public/index.php/api');

export const UPLOADS_BASE_URL = isDevelopment
  ? 'http://***********/tiffine/uploads'  // Use your computer's IP for uploads
  : (Constants.expoConfig?.extra?.uploadsBaseUrl || 'https://boxbites.in/public/uploads');

// App Colors - Green Theme
export const COLORS = {
  primary: '#4caf50',        // Main green color
  secondary: '#66bb6a',      // Lighter green
  accent: '#81c784',         // Light green accent
  background: '#f5f5f5',
  surface: '#ffffff',
  text: '#333333',
  textSecondary: '#666666',
  textLight: '#999999',
  border: '#e0e0e0',
  success: '#4caf50',        // Keep success green
  warning: '#ff9800',
  error: '#f44336',
  info: '#2196f3',
};

// App Fonts
export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
};

// App Sizes
export const SIZES = {
  // Global sizes
  base: 8,
  font: 14,
  radius: 8,
  padding: 16,
  margin: 16,

  // Font sizes
  largeTitle: 34,
  title1: 28,
  title2: 22,
  title3: 20,
  headline: 17,
  body: 17,
  callout: 16,
  subhead: 15,
  footnote: 13,
  caption1: 12,
  caption2: 11,

  // App dimensions
  width: 375,
  height: 812,
};

// API Endpoints
export const ENDPOINTS = {
  // Auth
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  
  // User
  PROFILE: '/user/profile',
  UPDATE_PROFILE: '/user/update-profile',
  
  // Dishes
  DISHES: '/dishes',
  DISH_DETAILS: '/dishes',
  
  // Cart
  CART: '/cart',
  ADD_TO_CART: '/cart/add',
  UPDATE_CART: '/cart/update',
  REMOVE_FROM_CART: '/cart/remove',
  CLEAR_CART: '/cart/clear',
  
  // Booking
  PLACE_ORDER: '/booking/place-order',
  BOOKINGS: '/bookings',
  BOOKING_DETAILS: '/bookings',
  CANCEL_BOOKING: '/bookings/cancel',
  
  // Reviews
  ADD_REVIEW: '/reviews/add',
  DISH_REVIEWS: '/reviews/dish',
  
  // Banners
  BANNERS: '/banners',
  
  // Wallet
  WALLET: '/wallet',
  WALLET_RECHARGE: '/wallet/recharge',

  // Settings
  SHIPPING_SETTINGS: '/settings/shipping',
  CALCULATE_SHIPPING: '/settings/calculate-shipping',
  APP_CONFIG: '/settings/config',
};

// Storage Keys
export const STORAGE_KEYS = {
  USER_TOKEN: 'userToken',
  USER_DATA: 'userData',
  CART_DATA: 'cartData',
  ONBOARDING_COMPLETED: 'onboardingCompleted',
};

// Order Status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PREPARING: 'preparing',
  OUT_FOR_DELIVERY: 'out_for_delivery',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
};

// Payment Methods
export const PAYMENT_METHODS = {
  WALLET: 'wallet',
  RAZORPAY: 'razorpay',
  COD: 'cod',
};
