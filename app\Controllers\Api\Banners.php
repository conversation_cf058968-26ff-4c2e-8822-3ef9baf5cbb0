<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Models\BannerModel;
use CodeIgniter\API\ResponseTrait;

class Banners extends BaseController
{
    use ResponseTrait;

    protected $bannerModel;

    public function __construct()
    {
        $this->bannerModel = new BannerModel();
    }

    public function index()
    {
        $banners = $this->bannerModel->getActiveBanners();

        // Format the banner data for the API
        $formattedBanners = [];
        foreach ($banners as $banner) {
            // Construct proper image URL
            $imageUrl = '';
            if (!empty($banner['image'])) {
                // Check if image already has full URL
                if (strpos($banner['image'], 'http') === 0) {
                    $imageUrl = $banner['image'];
                } else {
                    // Construct full URL for the image
                    $baseUrl = base_url('/public/uploads/banners/') . $banner['image'];

                    // For mobile app development, replace localhost with IP
                    if (ENVIRONMENT === 'development' && strpos($baseUrl, 'localhost') !== false) {
                        $imageUrl = str_replace('http://localhost/tiffine/', 'http://***********/tiffine/', $baseUrl);
                    } else {
                        $imageUrl = $baseUrl;
                    }
                }
            }

            $formattedBanners[] = [
                'id' => $banner['id'],
                'title' => $banner['title'],
                'subtitle' => $banner['subtitle'],
                'image' => $imageUrl,
                'image_url' => $imageUrl, // Add both for compatibility
                'button_text' => $banner['button_text'],
                'button_link' => $banner['button_link'],
                'order' => $banner['order'],
            ];
        }

        return $this->respond([
            'status' => true,
            'data' => $formattedBanners
        ]);
    }
}
