<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddCustomScriptsSetting extends Migration
{
    public function up()
    {
        // Add custom scripts setting
        $data = [
            'setting_key' => 'custom_scripts',
            'setting_value' => '',
            'setting_type' => 'text',
            'description' => 'Custom JavaScript code for chat widgets, analytics, etc.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        // Check if setting already exists before inserting
        $existing = $this->db->table('settings')
            ->where('setting_key', $data['setting_key'])
            ->get()
            ->getRow();
        
        if (!$existing) {
            $this->db->table('settings')->insert($data);
        }
    }

    public function down()
    {
        // Remove the custom scripts setting
        $this->db->table('settings')->where('setting_key', 'custom_scripts')->delete();
    }
}
