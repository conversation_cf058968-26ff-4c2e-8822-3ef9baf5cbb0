<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Models\SettingModel;

class Settings extends BaseController
{
    protected $settingModel;

    public function __construct()
    {
        $this->settingModel = new SettingModel();
    }

    /**
     * Get shipping settings for mobile app
     */
    public function shipping()
    {
        try {
            // Load shipping helper
            helper('shipping');
            
            // Get shipping settings
            $shippingEnabled = get_setting('shipping_enabled', '1') == '1';
            $shippingCharge = (float) get_setting('shipping_charge', 50);
            $freeShippingThreshold = (float) get_setting('free_shipping_threshold', 500);
            $shippingMessage = get_setting('shipping_message', 'Free shipping on orders above ₹500');
            $currencySymbol = get_setting('currency_symbol', '₹');

            return $this->response->setJSON([
                'status' => true,
                'data' => [
                    'shipping_enabled' => $shippingEnabled,
                    'shipping_charge' => $shippingCharge,
                    'free_shipping_threshold' => $freeShippingThreshold,
                    'shipping_message' => $shippingMessage,
                    'currency_symbol' => $currencySymbol
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON([
                'status' => false,
                'message' => 'Failed to fetch shipping settings',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Calculate shipping for given subtotal
     */
    public function calculateShipping()
    {
        try {
            $subtotal = (float) $this->request->getJSON()->subtotal ?? 0;

            if ($subtotal <= 0) {
                return $this->response->setStatusCode(400)->setJSON([
                    'status' => false,
                    'message' => 'Invalid subtotal amount'
                ]);
            }

            // Load shipping helper
            helper('shipping');
            
            // Calculate shipping
            $shippingData = calculate_shipping_charge($subtotal);

            return $this->response->setJSON([
                'status' => true,
                'data' => $shippingData
            ]);

        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON([
                'status' => false,
                'message' => 'Failed to calculate shipping',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get app configuration settings
     */
    public function config()
    {
        try {
            // Get common app settings
            $settings = [
                'app_name' => get_setting('app_name', 'Tiffin App'),
                'currency_symbol' => get_setting('currency_symbol', '₹'),
                'currency_code' => get_setting('currency_code', 'INR'),
                'timezone' => get_setting('timezone', 'Asia/Kolkata'),
                'contact_phone' => get_setting('contact_phone', ''),
                'contact_email' => get_setting('contact_email', ''),
                'support_message' => get_setting('support_message', 'Contact us for support'),
                
                // Shipping settings
                'shipping_enabled' => get_setting('shipping_enabled', '1') == '1',
                'shipping_charge' => (float) get_setting('shipping_charge', 50),
                'free_shipping_threshold' => (float) get_setting('free_shipping_threshold', 500),
                'shipping_message' => get_setting('shipping_message', 'Free shipping on orders above ₹500'),
                
                // Order settings
                'min_order_amount' => (float) get_setting('min_order_amount', 0),
                'max_order_amount' => (float) get_setting('max_order_amount', 0),
                'order_cancellation_time' => (int) get_setting('order_cancellation_time', 30), // minutes
            ];

            return $this->response->setJSON([
                'status' => true,
                'data' => $settings
            ]);

        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON([
                'status' => false,
                'message' => 'Failed to fetch app configuration',
                'error' => $e->getMessage()
            ]);
        }
    }
}
