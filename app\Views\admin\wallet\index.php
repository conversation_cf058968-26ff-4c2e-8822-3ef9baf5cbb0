<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-wallet text-primary"></i> Wallet Management
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?= base_url('/admin/dashboard') ?>">Dashboard</a></li>
                <li class="breadcrumb-item active">Wallet Management</li>
            </ol>
        </nav>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Search and Actions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">User Wallets</h6>
        </div>
        <div class="card-body">
            <!-- Search Form -->
            <form method="GET" class="mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search"
                                placeholder="Search by name, email, or phone..."
                                value="<?= esc($search) ?>">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if ($search): ?>
                            <a href="<?= base_url('/admin/wallet-management') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Clear Search
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>

            <!-- Users Table -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>User</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Wallet Balance</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No users found</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                                style="width: 40px; height: 40px;">
                                                <span class="fw-bold"><?= strtoupper(substr($user['name'], 0, 1)) ?></span>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= esc($user['name']) ?></div>
                                                <small class="text-muted">ID: <?= $user['id'] ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= esc($user['email']) ?></td>
                                    <td><?= esc($user['phone'] ?: 'Not provided') ?></td>
                                    <td>
                                        <span class="badge bg-<?= ($user['balance'] ?? 0) > 0 ? 'success' : 'secondary' ?> fs-6">
                                            ₹<?= number_format($user['balance'] ?? 0, 2) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-success btn-sm"
                                                onclick="showAddBalanceModal(<?= $user['id'] ?>, '<?= esc($user['name']) ?>')">
                                                <i class="fas fa-plus"></i> Add
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm"
                                                onclick="showDeductBalanceModal(<?= $user['id'] ?>, '<?= esc($user['name']) ?>', <?= $user['balance'] ?? 0 ?>)">
                                                <i class="fas fa-minus"></i> Deduct
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-history"></i> Recent Wallet Transactions
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>User</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recentTransactions)): ?>
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">No recent transactions</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($recentTransactions as $transaction): ?>
                                <tr>
                                    <td><?= date('M d, Y H:i', strtotime($transaction['created_at'])) ?></td>
                                    <td><?= esc($transaction['user_name']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $transaction['type'] === 'credit' ? 'success' : 'danger' ?>">
                                            <?= ucfirst($transaction['type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-<?= $transaction['type'] === 'credit' ? 'success' : 'danger' ?>">
                                            <?= $transaction['type'] === 'credit' ? '+' : '-' ?>₹<?= number_format($transaction['amount'], 2) ?>
                                        </span>
                                    </td>
                                    <td><?= esc($transaction['description']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Balance Modal -->
<div class="modal fade" id="addBalanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle"></i> Add Balance
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('/admin/wallet/add-balance') ?>" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">User</label>
                        <input type="text" class="form-control" id="addUserName" readonly>
                        <input type="hidden" name="user_id" id="addUserId">
                    </div>
                    <div class="mb-3">
                        <label for="addAmount" class="form-label">Amount (₹)</label>
                        <input type="number" class="form-control" name="amount" id="addAmount"
                            min="1" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="addDescription" class="form-label">Description</label>
                        <input type="text" class="form-control" name="description" id="addDescription"
                            placeholder="Reason for adding balance">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add Balance
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Deduct Balance Modal -->
<div class="modal fade" id="deductBalanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-minus-circle"></i> Deduct Balance
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('/admin/wallet/deduct-balance') ?>" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">User</label>
                        <input type="text" class="form-control" id="deductUserName" readonly>
                        <input type="hidden" name="user_id" id="deductUserId">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Current Balance</label>
                        <input type="text" class="form-control" id="currentBalance" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="deductAmount" class="form-label">Amount to Deduct (₹)</label>
                        <input type="number" class="form-control" name="amount" id="deductAmount"
                            min="1" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="deductDescription" class="form-label">Description</label>
                        <input type="text" class="form-control" name="description" id="deductDescription"
                            placeholder="Reason for deducting balance">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-minus"></i> Deduct Balance
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function showAddBalanceModal(userId, userName) {
        document.getElementById('addUserId').value = userId;
        document.getElementById('addUserName').value = userName;
        document.getElementById('addAmount').value = '';
        document.getElementById('addDescription').value = '';

        var modal = new bootstrap.Modal(document.getElementById('addBalanceModal'));
        modal.show();
    }

    function showDeductBalanceModal(userId, userName, currentBalance) {
        document.getElementById('deductUserId').value = userId;
        document.getElementById('deductUserName').value = userName;
        document.getElementById('currentBalance').value = '₹' + parseFloat(currentBalance).toFixed(2);
        document.getElementById('deductAmount').value = '';
        document.getElementById('deductAmount').max = currentBalance;
        document.getElementById('deductDescription').value = '';

        var modal = new bootstrap.Modal(document.getElementById('deductBalanceModal'));
        modal.show();
    }
</script>
<?= $this->endSection() ?>