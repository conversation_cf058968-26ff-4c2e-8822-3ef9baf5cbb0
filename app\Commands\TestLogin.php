<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\UserModel;

class TestLogin extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'test:login';
    protected $description = 'Test login functionality';

    public function run(array $params)
    {
        $email = $params[0] ?? '<EMAIL>';
        $password = $params[1] ?? '123456789';
        
        CLI::write("Testing login for: $email", 'yellow');
        CLI::write("Password: $password", 'yellow');
        
        $userModel = new UserModel();
        $user = $userModel->where('email', $email)->first();
        
        if (!$user) {
            CLI::write("User not found!", 'red');
            return;
        }
        
        CLI::write("User found: " . $user['name'], 'green');
        CLI::write("Stored hash: " . $user['password']);
        
        $isValid = password_verify($password, $user['password']);
        CLI::write("Login result: " . ($isValid ? 'SUCCESS' : 'FAILED'), $isValid ? 'green' : 'red');
        
        if ($isValid) {
            CLI::write("✅ User can now login successfully!", 'green');
        } else {
            CLI::write("❌ Login still failing", 'red');
        }
    }
}
