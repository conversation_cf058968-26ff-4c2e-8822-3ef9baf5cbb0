<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\BookingModel;
use App\Models\BookingItemModel;
use App\Models\DishModel;
use App\Models\DeliverySlotModel;
use App\Models\WalletModel;
use App\Models\WalletTransactionModel;
use Razorpay\Api\Api as RazorpayApi;

class Booking extends BaseController
{
    protected $bookingModel;
    protected $bookingItemModel;
    protected $dishModel;
    protected $deliverySlotModel;
    protected $walletModel;
    protected $walletTransactionModel;

    public function __construct()
    {
        $this->bookingModel = new BookingModel();
        $this->bookingItemModel = new BookingItemModel();
        $this->dishModel = new DishModel();
        $this->deliverySlotModel = new DeliverySlotModel();
        $this->walletModel = new WalletModel();
        $this->walletTransactionModel = new WalletTransactionModel();

        // Initialize cart sessions if not exists
        if (!session()->has('cart')) {
            session()->set('cart', []);
        }
        if (!session()->has('guest_cart')) {
            session()->set('guest_cart', []);
        }
    }

    public function index()
    {
        if (!session()->get('logged_in')) {
            return redirect()->to('/auth/login');
        }

        return redirect()->to('/booking/create');
    }

    public function create()
    {
        // Get cart based on login status
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        $cart = session()->get($cartKey) ?? [];

        $data['dishes'] = $this->dishModel->where('available', 1)->findAll();
        $data['slots'] = $this->deliverySlotModel->where('is_active', 1)->findAll();
        $data['cart'] = $cart;
        $data['total'] = $this->calculateTotal($cart);
        $data['is_guest'] = !session()->get('logged_in');

        return view('booking/create', $data);
    }

    public function addToCart($id)
    {
        $dish = $this->dishModel->find($id);

        if (!$dish || $dish['available'] != 1) {
            return redirect()->back()->with('error', 'Dish not available');
        }

        // Use guest cart if not logged in, user cart if logged in
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        $cart = session()->get($cartKey) ?? [];
        $quantity = $this->request->getGet('quantity') ?? 1;

        // Check if dish already in cart
        if (isset($cart[$id])) {
            $cart[$id]['quantity'] += $quantity;
        } else {
            $cart[$id] = [
                'id' => $dish['id'],
                'name' => $dish['name'],
                'price' => $dish['price'],
                'quantity' => $quantity,
                'is_vegetarian' => $dish['is_vegetarian'] ?? 1,
                'image' => $dish['image'] ?? null
            ];
        }

        session()->set($cartKey, $cart);

        $message = session()->get('logged_in')
            ? 'Dish added to cart'
            : 'Dish added to cart. Please login to proceed with checkout.';

        return redirect()->back()->with('success', $message);
    }

    public function removeFromCart($id)
    {
        // Use guest cart if not logged in, user cart if logged in
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        $cart = session()->get($cartKey) ?? [];

        if (isset($cart[$id])) {
            unset($cart[$id]);
            session()->set($cartKey, $cart);
        }

        return redirect()->back()->with('success', 'Dish removed from cart');
    }

    public function clearCart()
    {
        // Use guest cart if not logged in, user cart if logged in
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        session()->set($cartKey, []);

        return redirect()->back()->with('success', 'Cart cleared');
    }

    public function updateQuantity($id, $quantity)
    {
        if (!session()->get('logged_in')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You must be logged in to update cart'
            ]);
        }

        // Validate quantity
        $quantity = (int) $quantity;
        if ($quantity < 1 || $quantity > 10) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid quantity. Must be between 1 and 10.'
            ]);
        }

        // Use guest cart if not logged in, user cart if logged in
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        $cart = session()->get($cartKey) ?? [];

        // Check if item exists in cart
        if (!isset($cart[$id])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Item not found in cart'
            ]);
        }

        // Update quantity
        $cart[$id]['quantity'] = $quantity;
        session()->set($cartKey, $cart);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Quantity updated successfully'
        ]);
    }

    public function checkout()
    {
        if (!session()->get('logged_in')) {
            return redirect()->to('/auth/login');
        }

        $cart = session()->get('cart');

        if (empty($cart)) {
            return redirect()->to('/booking/create')->with('error', 'Your cart is empty');
        }

        $data['cart'] = $cart;
        $data['shipping_details'] = $this->calculateTotalWithDetails($cart);
        $data['total'] = $data['shipping_details']['total'];

        $userId = session()->get('user_id');
        $data['wallet'] = $this->walletModel->where('user_id', $userId)->first();

        return view('booking/checkout', $data);
    }

    public function createOrder()
    {
        if (!session()->get('logged_in')) {
            return $this->response->setJSON(['error' => 'User not logged in']);
        }

        $cart = session()->get('cart');

        if (empty($cart)) {
            return $this->response->setJSON(['error' => 'Your cart is empty']);
        }

        $shippingDetails = $this->calculateTotalWithDetails($cart);
        $totalAmount = $shippingDetails['total'];

        try {
            // Initialize Razorpay API
            $keyId = getenv('razorpay.key_id') ?: 'rzp_test_XaZ89XsD6ejHqt';
            $keySecret = getenv('razorpay.key_secret') ?: 'SoUETaL5nEG2tPNJe35Bz0fE';
            $api = new RazorpayApi($keyId, $keySecret);

            // Create order
            $orderData = [
                'receipt' => 'order_' . time(),
                'amount' => $totalAmount * 100, // Convert to paise
                'currency' => 'INR',
                'notes' => [
                    'user_id' => session()->get('user_id'),
                    'purpose' => 'order_payment'
                ]
            ];

            $order = $api->order->create($orderData);

            return $this->response->setJSON([
                'order_id' => $order->id,
                'amount' => $order->amount,
                'currency' => $order->currency
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Razorpay order creation failed: ' . $e->getMessage());
            return $this->response->setJSON(['error' => 'Failed to create payment order: ' . $e->getMessage()]);
        }
    }

    public function placeOrder()
    {
        if (!session()->get('logged_in')) {
            return redirect()->to('/auth/login');
        }

        $cart = session()->get('cart');

        if (empty($cart)) {
            return redirect()->to('/booking/create')->with('error', 'Your cart is empty');
        }

        $userId = session()->get('user_id');
        $paymentMethod = $this->request->getPost('payment_method');
        $shippingDetails = $this->calculateTotalWithDetails($cart);
        $totalAmount = $shippingDetails['total'];

        // Set default booking date to tomorrow
        $bookingDate = date('Y-m-d', strtotime('+1 day'));

        // Set default delivery slot (first active slot)
        $defaultSlot = $this->deliverySlotModel->where('is_active', 1)->first();
        $deliverySlotId = $defaultSlot ? $defaultSlot['id'] : null;

        // Handle payment based on method
        if ($paymentMethod == 'wallet') {
            // Check wallet balance
            $wallet = $this->walletModel->where('user_id', $userId)->first();

            if (!$wallet || $wallet['balance'] < $totalAmount) {
                return redirect()->back()->with('error', 'Insufficient wallet balance');
            }

            // Deduct from wallet
            $newBalance = $wallet['balance'] - $totalAmount;
            $this->walletModel->update($wallet['id'], ['balance' => $newBalance]);

            // Record transaction
            $this->walletTransactionModel->insert([
                'wallet_id' => $wallet['id'],
                'user_id' => $userId,
                'type' => 'debit',
                'amount' => $totalAmount,
                'description' => 'Payment for tiffin booking'
            ]);
        } elseif ($paymentMethod == 'razorpay') {
            // Verify Razorpay payment
            $razorpayPaymentId = $this->request->getPost('razorpay_payment_id');
            $razorpayOrderId = $this->request->getPost('razorpay_order_id');
            $razorpaySignature = $this->request->getPost('razorpay_signature');

            if (!$razorpayPaymentId || !$razorpayOrderId || !$razorpaySignature) {
                return redirect()->back()->with('error', 'Invalid payment data');
            }

            try {
                // Initialize Razorpay API
                $keyId = getenv('razorpay.key_id') ?: 'rzp_test_XaZ89XsD6ejHqt';
                $keySecret = getenv('razorpay.key_secret') ?: 'SoUETaL5nEG2tPNJe35Bz0fE';
                $api = new RazorpayApi($keyId, $keySecret);

                // Verify signature
                $attributes = [
                    'razorpay_payment_id' => $razorpayPaymentId,
                    'razorpay_order_id' => $razorpayOrderId,
                    'razorpay_signature' => $razorpaySignature
                ];

                $api->utility->verifyPaymentSignature($attributes);
            } catch (\Exception $e) {
                log_message('error', 'Razorpay payment verification failed: ' . $e->getMessage());
                return redirect()->back()->with('error', 'Payment verification failed: ' . $e->getMessage());
            }
        }

        // Create booking
        $bookingData = [
            'user_id' => $userId,
            'booking_date' => $bookingDate,
            'delivery_slot_id' => $deliverySlotId ?? null,
            'subtotal' => $shippingDetails['subtotal'],
            'shipping_charge' => $shippingDetails['shipping_charge'],
            'total_amount' => $totalAmount,
            'status' => 'pending',
            'payment_method' => $paymentMethod,
            'payment_id' => $paymentMethod == 'razorpay' ? $this->request->getPost('razorpay_payment_id') : null
        ];

        // Insert booking with error handling
        $bookingInserted = $this->bookingModel->insert($bookingData);

        if (!$bookingInserted) {
            $errors = $this->bookingModel->errors();
            log_message('error', 'Booking insertion failed: ' . json_encode($errors));
            return redirect()->back()->with('error', 'Failed to create booking. Please try again.');
        }

        $bookingId = $this->bookingModel->getInsertID();

        if (!$bookingId) {
            log_message('error', 'Booking ID not generated after insertion');
            return redirect()->back()->with('error', 'Failed to create booking. Please try again.');
        }

        // Add booking items with error handling
        foreach ($cart as $item) {
            $itemData = [
                'booking_id' => $bookingId,
                'dish_id' => $item['id'],
                'quantity' => $item['quantity'],
                'price' => $item['price']
            ];

            $itemInserted = $this->bookingItemModel->insert($itemData);

            if (!$itemInserted) {
                $errors = $this->bookingItemModel->errors();
                log_message('error', 'Booking item insertion failed: ' . json_encode($errors) . ' for booking_id: ' . $bookingId);
                // If item insertion fails, we should rollback the booking
                $this->bookingModel->delete($bookingId);
                return redirect()->back()->with('error', 'Failed to add items to booking. Please try again.');
            }
        }

        // Clear cart
        session()->set('cart', []);

        return redirect()->to('/user/bookings')->with('success', 'Booking placed successfully');
    }

    private function calculateTotal($cart)
    {
        $subtotal = 0;

        foreach ($cart as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }

        // Load shipping helper and calculate shipping
        helper('shipping');
        $shippingData = calculate_shipping_charge($subtotal);

        return $shippingData['total'];
    }

    private function calculateTotalWithDetails($cart)
    {
        $subtotal = 0;

        foreach ($cart as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }

        // Load shipping helper and calculate shipping
        helper('shipping');
        return format_shipping_details(calculate_shipping_charge($subtotal));
    }

    /**
     * Merge guest cart with user cart when user logs in
     */
    public function mergeGuestCart()
    {
        if (!session()->get('logged_in')) {
            return;
        }

        $guestCart = session()->get('guest_cart') ?? [];
        $userCart = session()->get('cart') ?? [];

        // Merge guest cart items into user cart
        foreach ($guestCart as $id => $item) {
            if (isset($userCart[$id])) {
                // If item already exists in user cart, add quantities
                $userCart[$id]['quantity'] += $item['quantity'];
            } else {
                // Add new item to user cart
                $userCart[$id] = $item;
            }
        }

        // Update user cart and clear guest cart
        session()->set('cart', $userCart);
        session()->set('guest_cart', []);
    }

    /**
     * Get cart data for sidebar (AJAX endpoint)
     */
    public function getCartData()
    {
        // Get cart based on login status
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        $cart = session()->get($cartKey) ?? [];

        if (empty($cart)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Cart is empty'
            ]);
        }

        $total = $this->calculateTotal($cart);

        return $this->response->setJSON([
            'success' => true,
            'cart' => $cart,
            'total' => $total
        ]);
    }

    /**
     * Get cart count for badges (AJAX endpoint)
     */
    public function getCartCount()
    {
        // Get cart based on login status
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        $cart = session()->get($cartKey) ?? [];

        $count = 0;
        foreach ($cart as $item) {
            $count += $item['quantity'];
        }

        return $this->response->setJSON([
            'success' => true,
            'count' => $count
        ]);
    }

    /**
     * Update cart quantity (AJAX endpoint)
     */
    public function updateCartQuantity()
    {
        $dishId = $this->request->getPost('dish_id');
        $quantity = (int) $this->request->getPost('quantity');

        if (!$dishId || $quantity < 1 || $quantity > 10) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid dish ID or quantity'
            ]);
        }

        // Get cart based on login status
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        $cart = session()->get($cartKey) ?? [];

        if (!isset($cart[$dishId])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Item not found in cart'
            ]);
        }

        // Update quantity
        $cart[$dishId]['quantity'] = $quantity;
        session()->set($cartKey, $cart);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Quantity updated successfully'
        ]);
    }

    /**
     * Remove item from cart (AJAX endpoint)
     */
    public function removeFromCartAjax()
    {
        $dishId = $this->request->getPost('dish_id');

        if (!$dishId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid dish ID'
            ]);
        }

        // Get cart based on login status
        $cartKey = session()->get('logged_in') ? 'cart' : 'guest_cart';
        $cart = session()->get($cartKey) ?? [];

        if (!isset($cart[$dishId])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Item not found in cart'
            ]);
        }

        // Remove item
        unset($cart[$dishId]);
        session()->set($cartKey, $cart);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Item removed from cart'
        ]);
    }
}
