import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import ApiService from '../../services/api';
import { useCart } from '../../utils/CartContext';
import { getImageUrl } from '../../utils/textUtils';
import { COLORS, SIZES } from '../../constants/config';

const { width } = Dimensions.get('window');

export default function DishDetailsScreen({ route, navigation }) {
  const { dishId, dish: initialDish } = route.params;
  const [dish, setDish] = useState(initialDish || null);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(!initialDish);
  const [quantity, setQuantity] = useState(1);

  const { addToCart, getCartItem, cartLoading } = useCart();

  useEffect(() => {
    if (!initialDish) {
      loadDishDetails();
    }
    loadReviews();
  }, [dishId]);

  const loadDishDetails = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getDishDetails(dishId);

      if (response.status && response.data) {
        setDish(response.data);
      } else {
        Alert.alert('Error', 'Failed to load dish details');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error loading dish details:', error);
      Alert.alert('Error', 'Failed to load dish details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const loadReviews = async () => {
    try {
      const response = await ApiService.getDishReviews(dishId);
      if (response.status && response.data) {
        setReviews(response.data);
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
    }
  };

  const handleAddToCart = async () => {
    if (!dish) return;

    const result = await addToCart(dish, quantity);
    if (result.success) {
      Alert.alert('Success', `${dish.name} added to cart!`);
    } else {
      Alert.alert('Error', result.error || 'Failed to add item to cart');
    }
  };

  const cartItem = getCartItem(dishId);
  const isInCart = !!cartItem;

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading dish details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!dish) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={COLORS.error} />
          <Text style={styles.errorText}>Dish not found</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Dish Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: getImageUrl(dish.image || dish.image_url, 'https://via.placeholder.com/400x300') }}
            style={styles.dishImage}
            resizeMode="cover"
          />

          {/* Back Button */}
          <TouchableOpacity
            style={styles.backIconButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.surface} />
          </TouchableOpacity>
        </View>

        {/* Dish Info */}
        <View style={styles.dishInfo}>
          <View style={styles.dishHeader}>
            <Text style={styles.dishName}>{dish.name}</Text>

            {dish.rating && (
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color={COLORS.warning} />
                <Text style={styles.ratingText}>{String(dish.rating)}</Text>
                <Text style={styles.ratingCount}>({String(reviews.length)} reviews)</Text>
              </View>
            )}
          </View>

          <Text style={styles.dishPrice}>₹{dish.price}</Text>

          {dish.description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.sectionTitle}>Description</Text>
              <Text style={styles.dishDescription}>{dish.description}</Text>
            </View>
          )}

          {/* Quantity Selector */}
          <View style={styles.quantityContainer}>
            <Text style={styles.sectionTitle}>Quantity</Text>
            <View style={styles.quantitySelector}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(Math.max(1, quantity - 1))}
              >
                <Ionicons name="remove" size={20} color={COLORS.primary} />
              </TouchableOpacity>

              <Text style={styles.quantityText}>{String(quantity)}</Text>

              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(quantity + 1)}
              >
                <Ionicons name="add" size={20} color={COLORS.primary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Reviews Section */}
          {reviews.length > 0 && (
            <View style={styles.reviewsContainer}>
              <Text style={styles.sectionTitle}>Reviews ({String(reviews.length)})</Text>
              {reviews.slice(0, 3).map((review, index) => (
                <View key={index} style={styles.reviewItem}>
                  <View style={styles.reviewHeader}>
                    <Text style={styles.reviewerName}>{review.user_name || 'Anonymous'}</Text>
                    <View style={styles.reviewRating}>
                      {[...Array(5)].map((_, i) => (
                        <Ionicons
                          key={i}
                          name="star"
                          size={12}
                          color={i < (review.rating || 0) ? COLORS.warning : COLORS.border}
                        />
                      ))}
                    </View>
                  </View>
                  {review.comment && (
                    <Text style={styles.reviewComment}>{review.comment}</Text>
                  )}
                </View>
              ))}

              {reviews.length > 3 && (
                <TouchableOpacity style={styles.viewAllReviews}>
                  <Text style={styles.viewAllReviewsText}>View all reviews</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Add to Cart Button */}
      <View style={styles.footer}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalPrice}>₹{(parseFloat(dish.price) * quantity).toFixed(2)}</Text>
        </View>

        <TouchableOpacity
          style={[
            styles.addToCartButton,
            isInCart && styles.addToCartButtonActive,
            cartLoading && styles.addToCartButtonDisabled
          ]}
          onPress={handleAddToCart}
          disabled={cartLoading}
        >
          {cartLoading ? (
            <ActivityIndicator color={COLORS.surface} />
          ) : (
            <>
              <Ionicons
                name={isInCart ? "checkmark" : "bag-add"}
                size={20}
                color={COLORS.surface}
              />
              <Text style={styles.addToCartButtonText}>
                {isInCart ? `In Cart (${String(cartItem.quantity)})` : 'Add to Cart'}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
    marginTop: SIZES.padding,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SIZES.padding,
  },
  errorText: {
    fontSize: SIZES.title2,
    color: COLORS.error,
    marginTop: SIZES.padding,
    marginBottom: SIZES.padding * 2,
  },
  backButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SIZES.padding * 2,
    paddingVertical: SIZES.padding,
    borderRadius: SIZES.radius,
  },
  backButtonText: {
    color: COLORS.surface,
    fontSize: SIZES.body,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
  },
  dishImage: {
    width: width,
    height: 300,
  },
  backIconButton: {
    position: 'absolute',
    top: 40,
    left: SIZES.padding,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dishInfo: {
    backgroundColor: COLORS.surface,
    borderTopLeftRadius: SIZES.padding,
    borderTopRightRadius: SIZES.padding,
    marginTop: -SIZES.padding,
    paddingHorizontal: SIZES.padding,
    paddingTop: SIZES.padding * 2,
    paddingBottom: SIZES.padding,
  },
  dishHeader: {
    marginBottom: SIZES.padding,
  },
  dishName: {
    fontSize: SIZES.title1,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SIZES.base,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: SIZES.callout,
    color: COLORS.text,
    marginLeft: 4,
    fontWeight: '600',
  },
  ratingCount: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
    marginLeft: 4,
  },
  dishPrice: {
    fontSize: SIZES.title2,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SIZES.padding * 2,
  },
  descriptionContainer: {
    marginBottom: SIZES.padding * 2,
  },
  sectionTitle: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SIZES.padding,
  },
  dishDescription: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
    lineHeight: 22,
  },
  quantityContainer: {
    marginBottom: SIZES.padding * 2,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: SIZES.radius,
    padding: SIZES.base,
    alignSelf: 'flex-start',
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  quantityText: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
    marginHorizontal: SIZES.padding * 2,
    minWidth: 40,
    textAlign: 'center',
  },
  reviewsContainer: {
    marginBottom: SIZES.padding,
  },
  reviewItem: {
    backgroundColor: COLORS.background,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    marginBottom: SIZES.base,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.base,
  },
  reviewerName: {
    fontSize: SIZES.callout,
    fontWeight: '600',
    color: COLORS.text,
  },
  reviewRating: {
    flexDirection: 'row',
  },
  reviewComment: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  viewAllReviews: {
    alignItems: 'center',
    paddingVertical: SIZES.base,
  },
  viewAllReviewsText: {
    fontSize: SIZES.callout,
    color: COLORS.primary,
    fontWeight: '600',
  },
  footer: {
    backgroundColor: COLORS.surface,
    paddingHorizontal: SIZES.padding,
    paddingVertical: SIZES.padding,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalContainer: {
    flex: 1,
    marginRight: SIZES.padding,
  },
  totalLabel: {
    fontSize: SIZES.callout,
    color: COLORS.textSecondary,
  },
  totalPrice: {
    fontSize: SIZES.title2,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  addToCartButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    paddingVertical: SIZES.padding,
    paddingHorizontal: SIZES.padding * 2,
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 140,
    justifyContent: 'center',
  },
  addToCartButtonActive: {
    backgroundColor: COLORS.success,
  },
  addToCartButtonDisabled: {
    opacity: 0.7,
  },
  addToCartButtonText: {
    color: COLORS.surface,
    fontSize: SIZES.body,
    fontWeight: 'bold',
    marginLeft: SIZES.base,
  },
});
