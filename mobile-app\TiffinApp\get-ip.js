const os = require('os');

function getLocalIPAddress() {
  const interfaces = os.networkInterfaces();
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        console.log(`Network Interface: ${name}`);
        console.log(`IP Address: ${interface.address}`);
        console.log('---');
      }
    }
  }
}

console.log('Finding your computer\'s IP address...\n');
getLocalIPAddress();
console.log('\nUse one of these IP addresses in your mobile app configuration.');
console.log('Update the API_BASE_URL in mobile-app/TiffinApp/src/constants/config.js');
console.log('Example: http://YOUR_IP_ADDRESS/tiffine/public/index.php/api');
