import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
// Removed bottom tab navigator - using header navigation instead

import { View, ActivityIndicator, Text } from 'react-native';
import { useAuth } from '../utils/AuthContext';

import { COLORS } from '../constants/config';

// Import screens (we'll create these next)
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import HomeScreen from '../screens/main/HomeScreen';
import MenuScreen from '../screens/main/MenuScreen';
import CartScreen from '../screens/main/CartScreen';
import ProfileScreen from '../screens/main/ProfileScreen';
import OrdersScreen from '../screens/main/OrdersScreen';
import DishDetailsScreen from '../screens/main/DishDetailsScreen';
import CheckoutScreen from '../screens/main/CheckoutScreen';
// import WalletScreen from '../screens/main/WalletScreen';

const Stack = createStackNavigator();
// Removed Tab navigator - using header navigation instead


// Auth Stack Navigator
function AuthStack() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
    </Stack.Navigator>
  );
}

// Main Stack Navigator updated to remove tab navigation

// Main Stack Navigator
function MainStack() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false, // Using custom header component
      }}
    >
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="Menu" component={MenuScreen} />
      <Stack.Screen name="Cart" component={CartScreen} />
      <Stack.Screen name="Orders" component={OrdersScreen} />
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen 
        name="DishDetails" 
        component={DishDetailsScreen}
        options={{ title: 'Dish Details' }}
      />
      <Stack.Screen
        name="Checkout"
        component={CheckoutScreen}
        options={{ title: 'Checkout' }}
      />
      {/* <Stack.Screen
        name="Wallet"
        component={WalletScreen}
        options={{ title: 'Wallet' }}
      /> */}
    </Stack.Navigator>
  );
}

// Loading Screen Component
function LoadingScreen() {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: COLORS.primary
    }}>
      <ActivityIndicator size="large" color={COLORS.surface} />
      <Text style={{
        color: COLORS.surface,
        marginTop: 16,
        fontSize: 16,
        fontWeight: '500'
      }}>
        Loading...
      </Text>
    </View>
  );
}

// Main App Navigator
export default function AppNavigator() {
  const { isLoading, isAuthenticated, user } = useAuth();

  // Debug logging
  useEffect(() => {
    console.log('AppNavigator: Auth state changed - isLoading:', isLoading, 'isAuthenticated:', isAuthenticated, 'user:', user);
  }, [isLoading, isAuthenticated, user]);

  if (isLoading) {
    console.log('AppNavigator: Showing loading screen');
    return <LoadingScreen />;
  }

  console.log('AppNavigator: Rendering', isAuthenticated ? 'MainStack' : 'AuthStack');

  return (
    <NavigationContainer>
      {isAuthenticated ? <MainStack /> : <AuthStack />}
    </NavigationContainer>
  );
}
