<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdatePaymentMethodEnum extends Migration
{
    public function up()
    {
        // Update payment_method enum to include all payment methods
        $this->forge->modifyColumn('bookings', [
            'payment_method' => [
                'type' => 'ENUM',
                'constraint' => ['wallet', 'cash', 'razorpay', 'cod'],
                'default' => 'wallet',
            ]
        ]);
    }

    public function down()
    {
        // Revert payment_method enum to original values
        $this->forge->modifyColumn('bookings', [
            'payment_method' => [
                'type' => 'ENUM',
                'constraint' => ['wallet', 'cash'],
                'default' => 'wallet',
            ]
        ]);
    }
}
