<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Models\UserModel;
use CodeIgniter\API\ResponseTrait;

class Auth extends BaseController
{
    use ResponseTrait;

    protected $userModel;

    public function __construct()
    {
        helper('jwt');
        $this->userModel = new UserModel();
    }

    /**
     * Login API endpoint
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function login()
    {
        $rules = [
            'email' => 'required|valid_email',
            'password' => 'required|min_length[6]'
        ];

        if (!$this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        // Get raw input data
        $jsonInput = $this->request->getJSON();

        // Try to get email and password from JSON input first
        if (!empty($jsonInput) && isset($jsonInput->email) && isset($jsonInput->password)) {
            $email = $jsonInput->email;
            $password = $jsonInput->password;
        } else {
            // Fall back to form data
            $email = $this->request->getPost('email');
            $password = $this->request->getPost('password');
        }

        // Log the input for debugging
        log_message('debug', 'Login attempt with email: ' . $email);

        // Find user by email (case-insensitive)
        $user = null;
        $users = $this->userModel->findAll();
        foreach ($users as $u) {
            if (strtolower($u['email']) === strtolower($email)) {
                $user = $u;
                break;
            }
        }

        if (!$user) {
            return $this->failNotFound('User not found with the provided email.');
        }

        // Debug password verification
        log_message('debug', 'Password verification for user: ' . $user['email']);
        log_message('debug', 'Provided password length: ' . strlen($password));
        log_message('debug', 'Stored password hash: ' . substr($user['password'], 0, 20) . '...');

        if (!password_verify($password, $user['password'])) {
            log_message('debug', 'Password verification failed for user: ' . $user['email']);
            return $this->fail('Invalid password.', 401);
        }

        log_message('debug', 'Password verification successful for user: ' . $user['email']);

        // Check if user is admin (if is_admin field exists)
        if (isset($user['is_admin']) && $user['is_admin']) {
            return $this->fail('Admin users cannot login through the mobile app.', 403);
        }

        // Generate JWT token
        $userData = [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email']
        ];

        $token = generateJWTToken($userData);

        $response = [
            'status' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'phone' => $user['phone'],
                    'address' => $user['address']
                ],
                'token' => $token
            ]
        ];

        return $this->respond($response);
    }

    /**
     * Register API endpoint
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function register()
    {
        // Log the request for debugging
        log_message('debug', 'Register API called with data: ' . json_encode($this->request->getPost()));
        log_message('debug', 'Raw input: ' . file_get_contents('php://input'));

        $rules = [
            'name' => 'required|min_length[3]',
            'email' => 'required|valid_email|is_unique[users.email]',
            'password' => 'required|min_length[6]',
            'phone' => 'required|min_length[10]',
            'address' => 'required|min_length[5]'
        ];

        if (!$this->validate($rules)) {
            log_message('debug', 'Validation failed: ' . json_encode($this->validator->getErrors()));
            return $this->failValidationErrors($this->validator->getErrors());
        }

        // Get input data from various sources to ensure we capture it
        $jsonInput = $this->request->getJSON();
        $postData = $this->request->getPost();

        // Try to get data from JSON input first, then fall back to form data
        $name = !empty($jsonInput) && isset($jsonInput->name) ? $jsonInput->name : $postData['name'];
        $email = !empty($jsonInput) && isset($jsonInput->email) ? $jsonInput->email : $postData['email'];
        $password = !empty($jsonInput) && isset($jsonInput->password) ? $jsonInput->password : $postData['password'];
        $phone = !empty($jsonInput) && isset($jsonInput->phone) ? $jsonInput->phone : $postData['phone'];
        $address = !empty($jsonInput) && isset($jsonInput->address) ? $jsonInput->address : $postData['address'];

        // Hash password and verify it works
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Verify the hash was created correctly
        if (!password_verify($password, $hashedPassword)) {
            log_message('error', 'Password hash verification failed during registration');
            return $this->fail('Password hashing failed. Please try again.', 500);
        }

        $data = [
            'name' => $name,
            'email' => $email,
            'password' => $hashedPassword,
            'phone' => $phone,
            'address' => $address,
            'is_admin' => 0
        ];

        log_message('debug', 'Attempting to insert user with verified password hash');

        $this->userModel->insert($data);
        $userId = $this->userModel->getInsertID();

        if (!$userId) {
            log_message('error', 'Failed to insert user into database');
            return $this->fail('Failed to create user account. Please try again later.', 500);
        }

        // Generate JWT token
        $userData = [
            'id' => $userId,
            'name' => $data['name'],
            'email' => $data['email']
        ];

        $token = generateJWTToken($userData);

        $response = [
            'status' => true,
            'message' => 'Registration successful',
            'data' => [
                'user' => [
                    'id' => $userId,
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'phone' => $data['phone'],
                    'address' => $data['address']
                ],
                'token' => $token
            ]
        ];

        log_message('debug', 'Registration successful for user: ' . $data['email']);

        return $this->respond($response, 201);
    }
}
