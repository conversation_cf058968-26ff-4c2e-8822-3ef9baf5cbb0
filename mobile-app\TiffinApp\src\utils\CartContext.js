import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants/config';
import ApiService from '../services/api';
import { getImageUrl } from './textUtils';

// Cart Context
const CartContext = createContext();

// Cart Actions
const CART_ACTIONS = {
  SET_CART: 'SET_CART',
  ADD_ITEM: 'ADD_ITEM',
  UPDATE_ITEM: 'UPDATE_ITEM',
  REMOVE_ITEM: 'REMOVE_ITEM',
  CLEAR_CART: 'CLEAR_CART',
  SET_LOADING: 'SET_LOADING',
};

// Cart Reducer
const cartReducer = (state, action) => {
  switch (action.type) {
    case CART_ACTIONS.SET_CART:
      return {
        ...state,
        items: action.payload,
        loading: false,
      };

    case CART_ACTIONS.ADD_ITEM:
      const existingItemIndex = state.items.findIndex(
        item => item.dish_id === action.payload.dish_id
      );

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const updatedItems = [...state.items];
        updatedItems[existingItemIndex].quantity += action.payload.quantity;
        return {
          ...state,
          items: updatedItems,
        };
      } else {
        // Add new item
        return {
          ...state,
          items: [...state.items, action.payload],
        };
      }

    case CART_ACTIONS.UPDATE_ITEM:
      return {
        ...state,
        items: state.items.map(item =>
          item.dish_id === action.payload.dish_id
            ? { ...item, quantity: action.payload.quantity }
            : item
        ),
      };

    case CART_ACTIONS.REMOVE_ITEM:
      return {
        ...state,
        items: state.items.filter(item => item.dish_id !== action.payload.dish_id),
      };

    case CART_ACTIONS.CLEAR_CART:
      return {
        ...state,
        items: [],
      };

    case CART_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };

    default:
      return state;
  }
};

// Initial State
const initialState = {
  items: [],
  loading: false,
};

// Cart Provider Component
export const CartProvider = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Load cart from storage on app start
  useEffect(() => {
    loadCartFromStorage();
  }, []);

  // Save cart to storage whenever items change
  useEffect(() => {
    saveCartToStorage();
  }, [state.items]);

  const loadCartFromStorage = async () => {
    try {
      const cartData = await AsyncStorage.getItem(STORAGE_KEYS.CART_DATA);
      if (cartData) {
        const parsedCart = JSON.parse(cartData);
        dispatch({ type: CART_ACTIONS.SET_CART, payload: parsedCart });
      }
    } catch (error) {
      console.error('Error loading cart from storage:', error);
    }
  };

  const saveCartToStorage = async () => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.CART_DATA, JSON.stringify(state.items));
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  };

  // Cart Actions
  const addToCart = async (dish, quantity = 1) => {
    try {
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: true });
      
      const cartItem = {
        dish_id: dish.id,
        dish_name: dish.name,
        dish_price: dish.price,
        dish_image: getImageUrl(dish.image || dish.image_url),
        quantity: quantity,
      };

      // Add to local state
      dispatch({ type: CART_ACTIONS.ADD_ITEM, payload: cartItem });

      // Sync with backend
      await ApiService.addToCart(dish.id, quantity);
      
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: false });
      return { success: true };
    } catch (error) {
      console.error('Error adding to cart:', error);
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: false });
      return { success: false, error: error.message };
    }
  };

  const updateCartItem = async (dishId, quantity) => {
    try {
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: true });
      
      if (quantity <= 0) {
        return removeFromCart(dishId);
      }

      // Update local state
      dispatch({ 
        type: CART_ACTIONS.UPDATE_ITEM, 
        payload: { dish_id: dishId, quantity } 
      });

      // Sync with backend
      await ApiService.updateCart(dishId, quantity);
      
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: false });
      return { success: true };
    } catch (error) {
      console.error('Error updating cart item:', error);
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: false });
      return { success: false, error: error.message };
    }
  };

  const removeFromCart = async (dishId) => {
    try {
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: true });
      
      // Remove from local state
      dispatch({ type: CART_ACTIONS.REMOVE_ITEM, payload: { dish_id: dishId } });

      // Sync with backend
      await ApiService.removeFromCart(dishId);
      
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: false });
      return { success: true };
    } catch (error) {
      console.error('Error removing from cart:', error);
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: false });
      return { success: false, error: error.message };
    }
  };

  const clearCart = async () => {
    try {
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: true });
      
      // Clear local state
      dispatch({ type: CART_ACTIONS.CLEAR_CART });

      // Sync with backend
      await ApiService.clearCart();
      
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: false });
      return { success: true };
    } catch (error) {
      console.error('Error clearing cart:', error);
      dispatch({ type: CART_ACTIONS.SET_LOADING, payload: false });
      return { success: false, error: error.message };
    }
  };

  // Calculate cart totals
  const getCartTotal = () => {
    return state.items.reduce((total, item) => {
      return total + (parseFloat(item.dish_price) * item.quantity);
    }, 0);
  };

  const getCartItemCount = () => {
    return state.items.reduce((count, item) => count + item.quantity, 0);
  };

  const getCartItem = (dishId) => {
    return state.items.find(item => item.dish_id === dishId);
  };

  const value = {
    cartItems: state.items,
    cartLoading: state.loading,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    getCartTotal,
    getCartItemCount,
    getCartItem,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

// Custom hook to use cart context
export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
