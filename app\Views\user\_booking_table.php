<?php if (empty($bookings)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> No bookings found in this category.
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Booking ID</th>
                    <th>Date</th>
                    <th>Amount</th>
                    <th>Payment Method</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($bookings as $booking): ?>
                    <tr>
                        <td>#<?= $booking['id'] ?></td>
                        <td><?= date('M d, Y', strtotime($booking['booking_date'])) ?></td>
                        <td>₹<?= number_format($booking['total_amount'], 2) ?></td>
                        <td>
                            <?php
                            $paymentMethod = strtolower($booking['payment_method'] ?? 'cash');
                            $badgeClass = '';
                            $displayText = '';

                            switch ($paymentMethod) {
                                case 'wallet':
                                    $badgeClass = 'success';
                                    $displayText = 'Wallet';
                                    break;
                                case 'razorpay':
                                    $badgeClass = 'primary';
                                    $displayText = 'Razorpay';
                                    break;
                                case 'cash':
                                case 'cod':
                                    $badgeClass = 'secondary';
                                    $displayText = 'COD';
                                    break;
                                default:
                                    $badgeClass = 'secondary';
                                    $displayText = ucfirst($paymentMethod);
                            }
                            ?>
                            <span class="badge bg-<?= $badgeClass ?>">
                                <i class="fas fa-<?= $paymentMethod == 'wallet' ? 'wallet' : ($paymentMethod == 'razorpay' ? 'credit-card' : 'money-bill-wave') ?>"></i>
                                <?= $displayText ?>
                            </span>
                        </td>
                        <td>
                            <?php
                            $statusClass = '';
                            switch ($booking['status']) {
                                case 'pending':
                                    $statusClass = 'warning';
                                    break;
                                case 'confirmed':
                                    $statusClass = 'primary';
                                    break;
                                case 'delivered':
                                    $statusClass = 'success';
                                    break;
                                case 'cancelled':
                                    $statusClass = 'danger';
                                    break;
                            }
                            ?>
                            <span class="badge bg-<?= $statusClass ?>">
                                <?= ucfirst($booking['status']) ?>
                            </span>
                        </td>
                        <td>
                            <a href="<?= base_url('/user/bookings/view/' . $booking['id']) ?>" class="btn btn-sm btn-info text-white">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="<?= base_url('/user/invoice/' . $booking['id']) ?>" class="btn btn-sm btn-success">
                                <i class="fas fa-file-invoice"></i> Invoice
                            </a>
                            <?php if ($booking['status'] == 'pending'): ?>
                                <a href="<?= base_url('/user/bookings/cancel/' . $booking['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to cancel this booking?')">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php endif; ?>