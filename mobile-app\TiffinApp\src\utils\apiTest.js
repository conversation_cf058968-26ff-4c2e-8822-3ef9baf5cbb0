import { API_BASE_URL } from '../constants/config';

// Simple API connectivity test
export const testApiConnection = async () => {
  try {
    console.log('Testing API connection to:', API_BASE_URL);
    
    const response = await fetch(`${API_BASE_URL}/test`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    console.log('Test response status:', response.status);
    console.log('Test response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.text();
    console.log('Test response data:', data);

    return {
      success: response.ok,
      status: response.status,
      data: data,
    };
  } catch (error) {
    console.error('API test failed:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

// Test specific endpoint
export const testEndpoint = async (endpoint, options = {}) => {
  try {
    const url = `${API_BASE_URL}${endpoint}`;
    console.log(`Testing endpoint: ${url}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      ...options,
    });

    console.log(`Response status for ${endpoint}:`, response.status);
    
    const data = await response.text();
    console.log(`Response data for ${endpoint}:`, data);

    return {
      success: response.ok,
      status: response.status,
      data: data,
    };
  } catch (error) {
    console.error(`Test failed for ${endpoint}:`, error);
    return {
      success: false,
      error: error.message,
    };
  }
};
