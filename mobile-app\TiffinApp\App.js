import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AppNavigator from './src/navigation/AppNavigator';
import { CartProvider } from './src/utils/CartContext';
import { AuthProvider } from './src/utils/AuthContext';
import SplashScreen from './src/components/SplashScreen';

export default function App() {
  const [isLoading, setIsLoading] = useState(true);

  const handleSplashFinish = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return (
      <>
        <SplashScreen onFinish={handleSplashFinish} />
        <StatusBar style="light" backgroundColor="#4caf50" />
      </>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <AuthProvider>
        <CartProvider>
          <AppNavigator />
          <StatusBar style="light" backgroundColor="#4caf50" />
        </CartProvider>
      </AuthProvider>
    </GestureHandlerRootView>
  );
}
