# BoxBites - Tiffin Service Mobile App

A React Native Expo mobile application for the BoxBites tiffin delivery service.

## Features

- **User Authentication**: Login and registration with JWT token-based authentication
- **Home Screen**: Featured dishes, banners, and quick actions
- **Navigation**: Bottom tab navigation with Home, Menu, Cart, Orders, and Profile
- **API Integration**: Connected to the existing BoxBites backend API
- **Premium Design**: Clean, user-friendly interface with consistent styling
- **Cross-Platform**: Works on iOS, Android, and Web

## Project Structure

```
src/
├── components/          # Reusable UI components
├── constants/          # App constants and configuration
│   └── config.js       # API URLs, colors, sizes, endpoints
├── navigation/         # Navigation setup
│   └── AppNavigator.js # Main navigation configuration
├── screens/           # App screens
│   ├── auth/          # Authentication screens
│   │   ├── LoginScreen.js
│   │   └── RegisterScreen.js
│   └── main/          # Main app screens
│       ├── HomeScreen.js
│       ├── MenuScreen.js
│       ├── CartScreen.js
│       ├── OrdersScreen.js
│       ├── ProfileScreen.js
│       ├── DishDetailsScreen.js
│       └── CheckoutScreen.js
├── services/          # API and business logic
│   ├── api.js         # API service for HTTP requests
│   └── auth.js        # Authentication service
└── utils/             # Utility functions
```

## Installation & Setup

1. **Prerequisites**:
   - Node.js (v16 or higher)
   - npm or yarn
   - Expo CLI (`npm install -g @expo/cli`)

2. **Install Dependencies**:
   ```bash
   cd mobile-app/TiffinApp
   npm install
   ```

3. **Start Development Server**:
   ```bash
   npm start
   ```

4. **Run on Different Platforms**:
   - **Web**: Press `w` in the terminal or run `npm run web`
   - **Android**: Press `a` in the terminal or run `npm run android`
   - **iOS**: Press `i` in the terminal or run `npm run ios` (macOS only)

## Configuration

### API Configuration

The app is configured to connect to the BoxBites backend API. Update the API base URL in `src/constants/config.js`:

```javascript
export const API_BASE_URL = 'http://boxbites.in/public/index.php/api';
```

For local development, you can change this to:
```javascript
export const API_BASE_URL = 'http://localhost/tiffine/public/index.php/api';
```

### App Configuration

Update app details in `app.json`:
- App name and display name
- Bundle identifier for iOS/Android
- App icons and splash screen
- API base URL in `extra.apiBaseUrl`

## Available Scripts

- `npm start` - Start Expo development server
- `npm run android` - Run on Android device/emulator
- `npm run ios` - Run on iOS device/simulator (macOS only)
- `npm run web` - Run in web browser

## Key Features Implemented

### Authentication Flow
- JWT token-based authentication
- Secure token storage using AsyncStorage
- Automatic login state management
- Login and registration forms with validation

### Navigation
- Stack navigation for auth flow
- Bottom tab navigation for main app
- Proper navigation state management
- Custom styling and icons

### API Integration
- Centralized API service
- Authentication headers management
- Error handling and logging
- Support for all backend endpoints

### UI/UX
- Premium design with consistent color scheme
- Responsive layout for different screen sizes
- Loading states and error handling
- User-friendly forms with validation

## Next Steps

The basic structure is complete. To continue development:

1. **Complete Core Screens**: Implement full functionality for Menu, Cart, Orders screens
2. **Add Cart Management**: Implement cart state management and persistence
3. **Implement Order Flow**: Complete checkout and order tracking
4. **Add Push Notifications**: For order updates and promotions
5. **Implement Offline Support**: Cache data for offline usage
6. **Add Payment Integration**: Integrate with Razorpay or other payment gateways
7. **Testing**: Add unit and integration tests
8. **Performance Optimization**: Optimize images, API calls, and rendering

## Testing

To test the app:

1. Start the development server: `npm start`
2. Open in web browser by pressing `w`
3. Test authentication flow with existing user credentials
4. Navigate through different screens using bottom tabs
5. Test API integration with the backend

## Deployment

For production deployment:

1. **Build for Production**:
   ```bash
   expo build:android  # For Android
   expo build:ios      # For iOS
   ```

2. **Update API URLs**: Ensure production API URLs are configured

3. **App Store Submission**: Follow Expo's guide for app store submission

## Support

For issues or questions:
- Check Expo documentation: https://docs.expo.dev/
- React Navigation docs: https://reactnavigation.org/
- BoxBites backend API documentation

## License

This project is part of the BoxBites tiffin service application.
