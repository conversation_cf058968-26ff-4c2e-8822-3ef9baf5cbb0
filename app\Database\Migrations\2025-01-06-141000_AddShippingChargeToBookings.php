<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddShippingChargeToBookings extends Migration
{
    public function up()
    {
        // Add shipping_charge and subtotal columns to bookings table
        $fields = [
            'subtotal' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'default' => 0.00,
                'after' => 'delivery_slot_id'
            ],
            'shipping_charge' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'default' => 0.00,
                'after' => 'subtotal'
            ]
        ];
        
        $this->forge->addColumn('bookings', $fields);
    }

    public function down()
    {
        // Remove the added columns
        $this->forge->dropColumn('bookings', ['subtotal', 'shipping_charge']);
    }
}
