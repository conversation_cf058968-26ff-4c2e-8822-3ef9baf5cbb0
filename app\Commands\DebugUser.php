<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\UserModel;

class DebugUser extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'debug:user';
    protected $description = 'Debug user login issues';

    public function run(array $params)
    {
        $userModel = new UserModel();

        // Check if user exists
        $email = '<EMAIL>';
        CLI::write("Checking user: $email", 'yellow');

        $user = $userModel->where('email', $email)->first();

        if ($user) {
            CLI::write("User found:", 'green');
            CLI::write("ID: " . $user['id']);
            CLI::write("Name: " . $user['name']);
            CLI::write("Email: " . $user['email']);
            CLI::write("Password hash: " . substr($user['password'], 0, 30) . "...");
            CLI::write("Password hash length: " . strlen($user['password']));

            // Test password verification
            $testPasswords = ['123456789', '12345678', 'password123', 'krish123@'];

            CLI::write("\nTesting passwords:", 'yellow');
            CLI::write("Current password hash: " . $user['password']);

            foreach ($testPasswords as $testPassword) {
                CLI::write("Testing password: '$testPassword'");
                CLI::write("Hash: " . $user['password']);
                $isValid = password_verify($testPassword, $user['password']);
                $status = $isValid ? 'VALID' : 'INVALID';
                $color = $isValid ? 'green' : 'red';
                CLI::write("Result: $status", $color);
                CLI::write("---");
            }

            // Test with fresh hash
            CLI::write("\nTesting with fresh hash:", 'yellow');
            $freshHash = password_hash('123456789', PASSWORD_DEFAULT);
            CLI::write("Fresh hash: $freshHash");
            $freshTest = password_verify('123456789', $freshHash);
            CLI::write("Fresh hash test: " . ($freshTest ? 'PASSED' : 'FAILED'), $freshTest ? 'green' : 'red');
        } else {
            CLI::write("User not found with email: $email", 'red');

            // Show all users
            CLI::write("\nAll users in database:", 'yellow');
            $allUsers = $userModel->findAll();
            foreach ($allUsers as $u) {
                CLI::write("- ID: {$u['id']}, Name: {$u['name']}, Email: {$u['email']}");
            }
        }
    }
}
