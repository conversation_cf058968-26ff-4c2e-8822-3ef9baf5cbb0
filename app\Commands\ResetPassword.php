<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\UserModel;

class ResetPassword extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'reset:password';
    protected $description = 'Reset user password for testing';

    public function run(array $params)
    {
        $userModel = new UserModel();
        
        $email = $params[0] ?? '<EMAIL>';
        $newPassword = $params[1] ?? '123456789';
        
        CLI::write("Resetting password for: $email", 'yellow');
        CLI::write("New password: $newPassword", 'yellow');
        
        $user = $userModel->where('email', $email)->first();
        
        if ($user) {
            // Hash the new password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            
            // Update the user's password
            $userModel->update($user['id'], ['password' => $hashedPassword]);
            
            CLI::write("Password updated successfully!", 'green');
            CLI::write("New password hash: " . substr($hashedPassword, 0, 30) . "...");
            
            // Verify the password works
            $verification = password_verify($newPassword, $hashedPassword);
            CLI::write("Password verification test: " . ($verification ? 'PASSED' : 'FAILED'), $verification ? 'green' : 'red');
            
        } else {
            CLI::write("User not found with email: $email", 'red');
        }
    }
}
