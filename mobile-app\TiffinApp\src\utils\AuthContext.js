import React, { createContext, useContext, useState, useEffect } from 'react';
import AuthService from '../services/auth';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      console.log('AuthContext: Checking auth status...');
      const authStatus = await AuthService.checkAuthStatus();
      console.log('AuthContext: Auth status result:', authStatus);
      setIsAuthenticated(authStatus.isAuthenticated);
      setUser(authStatus.user);
      console.log('AuthContext: State set - isAuthenticated:', authStatus.isAuthenticated, 'user:', authStatus.user);
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      const result = await AuthService.login(email, password);
      if (result.success) {
        console.log('AuthContext: Login successful, updating state');
        setIsAuthenticated(true);
        setUser(result.user);
        console.log('AuthContext: State updated - isAuthenticated: true, user:', result.user);
        return result;
      }
      return result;
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      await AuthService.logout();
      setIsAuthenticated(false);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const register = async (userData) => {
    try {
      const result = await AuthService.register(userData);
      if (result.success) {
        console.log('AuthContext: Registration successful, updating state');
        setIsAuthenticated(true);
        setUser(result.user);
        console.log('AuthContext: State updated - isAuthenticated: true, user:', result.user);
        return result;
      }
      return result;
    } catch (error) {
      console.error('Register error:', error);
      return { success: false, error: error.message };
    }
  };

  const value = {
    isLoading,
    isAuthenticated,
    user,
    login,
    logout,
    register,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
