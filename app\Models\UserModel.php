<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table            = 'users';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['name', 'email', 'password', 'address', 'phone'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'name'     => 'required|min_length[3]|max_length[100]',
        'email'    => 'required|valid_email|is_unique[users.email,id,{id}]',
        'password' => 'required|min_length[6]',
        'phone'    => 'permit_empty|numeric|min_length[10]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['hashPassword'];
    protected $afterInsert    = ['createWallet'];
    protected $beforeUpdate   = ['hashPassword'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    protected function hashPassword(array $data)
    {
        if (! isset($data['data']['password'])) {
            return $data;
        }

        // Check if password is already hashed (starts with $2y$ or similar)
        $password = $data['data']['password'];
        if (preg_match('/^\$2[ayb]\$\d+\$/', $password)) {
            // Password is already hashed, don't hash again
            return $data;
        }

        // Only hash if it's a plain text password
        $data['data']['password'] = password_hash($password, PASSWORD_DEFAULT);

        return $data;
    }

    protected function createWallet(array $data)
    {
        if (isset($data['id']) && $data['result'] === true) {
            $walletModel = new WalletModel();
            $walletModel->insert([
                'user_id' => $data['id'],
                'balance' => 0.00
            ]);
        }

        return $data;
    }
}
