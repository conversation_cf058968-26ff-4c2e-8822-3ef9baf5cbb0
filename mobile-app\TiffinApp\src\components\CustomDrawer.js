import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

import AuthService from '../services/auth';
import { COLORS, SIZES } from '../constants/config';

export default function CustomDrawer({ navigation, ...props }) {
  const handleLogout = async () => {
    try {
      await AuthService.logout();
      navigation.reset({
        index: 0,
        routes: [{ name: 'Auth' }],
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const menuItems = [
    {
      name: 'Home',
      icon: 'home-outline',
      route: 'Home',
    },
    {
      name: 'Menu',
      icon: 'restaurant-outline',
      route: 'Menu',
    },
    {
      name: 'Cart',
      icon: 'bag-outline',
      route: 'Cart',
    },
    {
      name: 'Orders',
      icon: 'receipt-outline',
      route: 'Orders',
    },
    {
      name: 'Profile',
      icon: 'person-outline',
      route: 'Profile',
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <DrawerContentScrollView {...props} contentContainerStyle={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.profileSection}>
            <View style={styles.avatarContainer}>
              <Ionicons name="person" size={40} color={COLORS.primary} />
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>Welcome!</Text>
              <Text style={styles.userEmail}>BoxBites User</Text>
            </View>
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.menuSection}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={() => navigation.navigate(item.route)}
            >
              <Ionicons name={item.icon} size={24} color={COLORS.text} />
              <Text style={styles.menuText}>{item.name}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Divider */}
        <View style={styles.divider} />

        {/* Additional Options */}
        <View style={styles.additionalSection}>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="help-circle-outline" size={24} color={COLORS.text} />
            <Text style={styles.menuText}>Help & Support</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="information-circle-outline" size={24} color={COLORS.text} />
            <Text style={styles.menuText}>About</Text>
          </TouchableOpacity>
        </View>
      </DrawerContentScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Ionicons name="log-out-outline" size={24} color={COLORS.error} />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.surface,
  },
  scrollView: {
    flexGrow: 1,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingVertical: SIZES.padding * 2,
    paddingHorizontal: SIZES.padding,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.padding,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.surface,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: SIZES.body,
    color: COLORS.surface,
    opacity: 0.8,
  },
  menuSection: {
    paddingVertical: SIZES.padding,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SIZES.padding,
    paddingHorizontal: SIZES.padding * 2,
  },
  menuText: {
    fontSize: SIZES.body,
    color: COLORS.text,
    marginLeft: SIZES.padding,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginHorizontal: SIZES.padding,
  },
  additionalSection: {
    paddingVertical: SIZES.padding,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingVertical: SIZES.padding,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SIZES.padding,
    paddingHorizontal: SIZES.padding * 2,
  },
  logoutText: {
    fontSize: SIZES.body,
    color: COLORS.error,
    marginLeft: SIZES.padding,
    fontWeight: '500',
  },
});
