<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\UserModel;

class TestRegistration extends BaseCommand
{
    protected $group       = 'Debug';
    protected $name        = 'test:registration';
    protected $description = 'Test the registration process to find the issue';

    public function run(array $params)
    {
        $userModel = new UserModel();
        
        // Test data
        $testEmail = '<EMAIL>';
        $testPassword = '123456789';
        
        CLI::write("Testing registration process...", 'yellow');
        CLI::write("Email: $testEmail");
        CLI::write("Password: $testPassword");
        
        // Delete test user if exists
        $existingUser = $userModel->where('email', $testEmail)->first();
        if ($existingUser) {
            $userModel->delete($existingUser['id']);
            CLI::write("Deleted existing test user", 'yellow');
        }
        
        // Simulate the registration process exactly as the API does
        CLI::write("\n=== Testing Password Hashing ===", 'cyan');
        
        // Test 1: Direct password_hash
        $hash1 = password_hash($testPassword, PASSWORD_DEFAULT);
        $verify1 = password_verify($testPassword, $hash1);
        CLI::write("Direct hash test: " . ($verify1 ? 'PASSED' : 'FAILED'), $verify1 ? 'green' : 'red');
        CLI::write("Hash: $hash1");
        
        // Test 2: Simulate API registration data processing
        $jsonInput = (object)[
            'name' => 'Test User',
            'email' => $testEmail,
            'password' => $testPassword,
            'phone' => '1234567890',
            'address' => 'Test Address'
        ];
        
        // Extract data like the API does
        $name = $jsonInput->name;
        $email = $jsonInput->email;
        $password = $jsonInput->password;
        $phone = $jsonInput->phone;
        $address = $jsonInput->address;
        
        CLI::write("\nExtracted password: '$password'");
        CLI::write("Password length: " . strlen($password));
        CLI::write("Password type: " . gettype($password));
        
        // Hash like the API does
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $verifyBeforeStore = password_verify($password, $hashedPassword);
        
        CLI::write("API-style hash test: " . ($verifyBeforeStore ? 'PASSED' : 'FAILED'), $verifyBeforeStore ? 'green' : 'red');
        CLI::write("Hash: $hashedPassword");
        
        // Test 3: Insert into database and verify
        CLI::write("\n=== Testing Database Storage ===", 'cyan');
        
        $data = [
            'name' => $name,
            'email' => $email,
            'password' => $hashedPassword,
            'phone' => $phone,
            'address' => $address,
            'is_admin' => 0
        ];
        
        $userId = $userModel->insert($data);
        
        if ($userId) {
            CLI::write("User inserted with ID: $userId", 'green');
            
            // Retrieve and test
            $storedUser = $userModel->find($userId);
            CLI::write("Retrieved password hash: " . $storedUser['password']);
            CLI::write("Hash matches: " . ($storedUser['password'] === $hashedPassword ? 'YES' : 'NO'));
            
            $finalVerify = password_verify($testPassword, $storedUser['password']);
            CLI::write("Final verification: " . ($finalVerify ? 'PASSED' : 'FAILED'), $finalVerify ? 'green' : 'red');
            
            // Clean up
            $userModel->delete($userId);
            CLI::write("Test user deleted", 'yellow');
            
        } else {
            CLI::write("Failed to insert user", 'red');
        }
    }
}
