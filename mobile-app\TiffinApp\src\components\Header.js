import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import AuthService from '../services/auth';
import { useAuth } from '../utils/AuthContext';
import { useCart } from '../utils/CartContext';
import { COLORS, SIZES } from '../constants/config';

export default function Header({ title = 'BoxBites', navigation, currentRoute = 'Home' }) {
  const [menuVisible, setMenuVisible] = useState(false);
  const { logout } = useAuth();
  const { getCartItemCount } = useCart();

  const handleLogout = async () => {
    try {
      await logout();
      setMenuVisible(false);
      // Navigation will be handled automatically by AppNavigator
      // when authentication status changes via AuthContext
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const menuItems = [
    {
      name: 'Home',
      icon: 'home-outline',
      route: 'Home',
    },
    {
      name: 'Menu',
      icon: 'restaurant-outline',
      route: 'Menu',
    },
    {
      name: 'Cart',
      icon: 'bag-outline',
      route: 'Cart',
    },
    {
      name: 'Orders',
      icon: 'receipt-outline',
      route: 'Orders',
    },
    {
      name: 'Profile',
      icon: 'person-outline',
      route: 'Profile',
    },
  ];

  const handleMenuItemPress = (route) => {
    setMenuVisible(false);
    navigation.navigate(route);
  };

  const navigationItems = [
    { name: 'Home', icon: 'home', route: 'Home' },
    { name: 'Menu', icon: 'restaurant', route: 'Menu' },
    { name: 'Cart', icon: 'bag', route: 'Cart' },
    { name: 'Orders', icon: 'list', route: 'Orders' },
    { name: 'Profile', icon: 'person', route: 'Profile' },
  ];

  const handleNavigation = (route) => {
    if (navigation && route !== currentRoute) {
      navigation.navigate(route);
    }
  };

  return (
    <>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>

        <View style={styles.navigationContainer}>
          {navigationItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.navButton,
                currentRoute === item.route && styles.activeNavButton
              ]}
              onPress={() => handleNavigation(item.route)}
            >
              <View style={styles.navIconContainer}>
                <Ionicons
                  name={currentRoute === item.route ? item.icon : `${item.icon}-outline`}
                  size={20}
                  color={currentRoute === item.route ? COLORS.surface : COLORS.surface}
                />
                {item.route === 'Cart' && getCartItemCount() > 0 && (
                  <View style={styles.cartBadge}>
                    <Text style={styles.cartBadgeText}>
                      {getCartItemCount() > 9 ? '9+' : String(getCartItemCount() || 0)}
                    </Text>
                  </View>
                )}
              </View>
              <Text style={[
                styles.navText,
                currentRoute === item.route && styles.activeNavText
              ]}>
                {item.name || ''}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => setMenuVisible(true)}
        >
          <Ionicons name="menu" size={24} color={COLORS.surface} />
        </TouchableOpacity>
      </View>

      <Modal
        visible={menuVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setMenuVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.menuContainer}>
            <SafeAreaView style={styles.menuContent}>
              {/* Header */}
              <View style={styles.menuHeader}>
                <View style={styles.profileSection}>
                  <View style={styles.avatarContainer}>
                    <Ionicons name="person" size={40} color={COLORS.primary} />
                  </View>
                  <View style={styles.userInfo}>
                    <Text style={styles.userName}>Welcome!</Text>
                    <Text style={styles.userEmail}>BoxBites User</Text>
                  </View>
                </View>
                
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setMenuVisible(false)}
                >
                  <Ionicons name="close" size={24} color={COLORS.surface} />
                </TouchableOpacity>
              </View>

              {/* Menu Items */}
              <ScrollView style={styles.menuItems}>
                {menuItems.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.menuItem}
                    onPress={() => handleMenuItemPress(item.route)}
                  >
                    <Ionicons name={item.icon} size={24} color={COLORS.text} />
                    <Text style={styles.menuText}>{item.name}</Text>
                  </TouchableOpacity>
                ))}

                {/* Divider */}
                <View style={styles.divider} />

                {/* Additional Options */}
                <TouchableOpacity style={styles.menuItem}>
                  <Ionicons name="help-circle-outline" size={24} color={COLORS.text} />
                  <Text style={styles.menuText}>Help & Support</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.menuItem}>
                  <Ionicons name="information-circle-outline" size={24} color={COLORS.text} />
                  <Text style={styles.menuText}>About</Text>
                </TouchableOpacity>

                {/* Logout */}
                <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                  <Ionicons name="log-out-outline" size={24} color={COLORS.error} />
                  <Text style={styles.logoutText}>Logout</Text>
                </TouchableOpacity>
              </ScrollView>
            </SafeAreaView>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    backgroundColor: COLORS.primary,
    flexDirection: 'column',
    paddingHorizontal: SIZES.padding,
    paddingTop: SIZES.padding,
    paddingBottom: SIZES.base,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: SIZES.title2,
    fontWeight: 'bold',
    color: COLORS.surface,
    textAlign: 'center',
    marginBottom: SIZES.base,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: SIZES.base,
  },
  navButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SIZES.base / 2,
    paddingHorizontal: SIZES.base,
    borderRadius: SIZES.radius,
    minWidth: 50,
  },
  activeNavButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  navIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  navText: {
    fontSize: 10,
    color: COLORS.surface,
    marginTop: 2,
    opacity: 0.8,
  },
  activeNavText: {
    opacity: 1,
    fontWeight: '600',
  },
  cartBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: COLORS.error,
    borderRadius: 10,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  cartBadgeText: {
    color: COLORS.surface,
    fontSize: 10,
    fontWeight: 'bold',
  },
  menuButton: {
    position: 'absolute',
    top: SIZES.padding,
    right: SIZES.padding,
    padding: SIZES.base,
    zIndex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  menuContainer: {
    flex: 1,
    maxWidth: 280,
    backgroundColor: COLORS.surface,
  },
  menuContent: {
    flex: 1,
  },
  menuHeader: {
    backgroundColor: COLORS.primary,
    paddingVertical: SIZES.padding * 2,
    paddingHorizontal: SIZES.padding,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SIZES.padding,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.surface,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: SIZES.body,
    color: COLORS.surface,
    opacity: 0.8,
  },
  closeButton: {
    padding: SIZES.base,
  },
  menuItems: {
    flex: 1,
    paddingVertical: SIZES.padding,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SIZES.padding,
    paddingHorizontal: SIZES.padding * 2,
  },
  menuText: {
    fontSize: SIZES.body,
    color: COLORS.text,
    marginLeft: SIZES.padding,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginHorizontal: SIZES.padding,
    marginVertical: SIZES.padding,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SIZES.padding,
    paddingHorizontal: SIZES.padding * 2,
    marginTop: SIZES.padding,
  },
  logoutText: {
    fontSize: SIZES.body,
    color: COLORS.error,
    marginLeft: SIZES.padding,
    fontWeight: '500',
  },
});
