// Shipping calculation utilities
// Fetches shipping settings from backend API

import ApiService from '../services/api';

// Cache for shipping settings to avoid repeated API calls
let shippingSettingsCache = null;
let cacheTimestamp = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get shipping settings from API with caching
 */
export const getShippingSettings = async () => {
  const now = Date.now();

  // Return cached data if still valid
  if (shippingSettingsCache && cacheTimestamp && (now - cacheTimestamp) < CACHE_DURATION) {
    return shippingSettingsCache;
  }

  try {
    const response = await ApiService.getShippingSettings();
    if (response.status) {
      shippingSettingsCache = response.data;
      cacheTimestamp = now;
      return response.data;
    } else {
      throw new Error('Failed to fetch shipping settings');
    }
  } catch (error) {
    console.error('Error fetching shipping settings:', error);
    // Return default settings as fallback
    return {
      shipping_enabled: true,
      shipping_charge: 30,
      free_shipping_threshold: 500,
      shipping_message: 'Free shipping on orders above ₹500',
      currency_symbol: '₹'
    };
  }
};

/**
 * Calculate shipping charge using backend settings
 */
export const calculateShippingCharge = async (subtotal) => {
  const settings = await getShippingSettings();

  let shippingCharge = 0;

  if (settings.shipping_enabled) {
    // Check if order qualifies for free shipping
    if (settings.free_shipping_threshold > 0 && subtotal >= settings.free_shipping_threshold) {
      shippingCharge = 0; // Free shipping
    } else {
      shippingCharge = settings.shipping_charge;
    }
  }
  // If shipping is disabled, shippingCharge remains 0 (free)

  const total = subtotal + shippingCharge;

  return {
    subtotal: subtotal,
    shipping_charge: shippingCharge,
    total: total,
    is_free_shipping: shippingCharge === 0,
    free_shipping_threshold: settings.free_shipping_threshold,
    shipping_enabled: settings.shipping_enabled,
    currency_symbol: settings.currency_symbol
  };
};

/**
 * Format shipping details with currency symbols
 */
export const formatShippingDetails = async (subtotal) => {
  const details = await calculateShippingCharge(subtotal);
  const currencySymbol = details.currency_symbol || '₹';

  return {
    ...details,
    subtotal_formatted: `${currencySymbol}${details.subtotal.toFixed(2)}`,
    shipping_charge_formatted: details.shipping_charge === 0 ? 'Free' : `${currencySymbol}${details.shipping_charge.toFixed(2)}`,
    total_formatted: `${currencySymbol}${details.total.toFixed(2)}`
  };
};

/**
 * Clear shipping settings cache (useful for testing or when settings change)
 */
export const clearShippingCache = () => {
  shippingSettingsCache = null;
  cacheTimestamp = null;
};
