// Utility functions for safe text rendering
import { UPLOADS_BASE_URL } from '../constants/config';

export const safeText = (value, fallback = '') => {
  if (value === null || value === undefined) {
    return fallback;
  }

  // Convert to string and clean up
  let text = String(value);

  // Remove all types of line breaks and control characters
  text = text.replace(/[\r\n\t\f\v]/g, ' ');

  // Replace multiple spaces with single space
  text = text.replace(/\s+/g, ' ');

  // Trim whitespace
  text = text.trim();

  // Return fallback if empty after cleaning
  return text || fallback;
};

export const safeNumber = (value, fallback = 0) => {
  const num = parseFloat(value);
  return isNaN(num) ? fallback : num;
};

export const formatPrice = (price) => {
  const numPrice = safeNumber(price);
  return `₹${numPrice.toFixed(2)}`;
};

export const formatRating = (rating) => {
  const numRating = safeNumber(rating);
  return numRating > 0 ? numRating.toFixed(1) : '0.0';
};

export const safeBalance = (balance) => {
  return parseFloat(balance) || 0;
};

export const getImageUrl = (imageUrl, fallback = 'https://via.placeholder.com/300x200') => {
  if (!imageUrl) return fallback;

  // If the URL already contains localhost, replace it with the proper IP/domain
  if (imageUrl.includes('localhost')) {
    return imageUrl.replace('http://localhost/tiffine/uploads', UPLOADS_BASE_URL);
  }

  // If the URL contains the old development IP, replace it with production URL
  if (imageUrl.includes('***********')) {
    return imageUrl.replace('http://***********/tiffine/uploads', UPLOADS_BASE_URL);
  }

  // If it's a relative path, prepend the uploads base URL
  if (imageUrl.startsWith('/uploads') || imageUrl.startsWith('uploads')) {
    return `${UPLOADS_BASE_URL}/${imageUrl.replace(/^\/?(uploads\/)?/, '')}`;
  }

  // If it's already a full URL, return as is
  if (imageUrl.startsWith('http')) {
    return imageUrl;
  }

  // Default case - assume it's a filename and prepend the uploads URL
  return `${UPLOADS_BASE_URL}/${imageUrl}`;
};
