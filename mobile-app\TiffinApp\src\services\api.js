import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL, STORAGE_KEYS } from '../constants/config';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Get auth token from storage
  async getAuthToken() {
    try {
      const token = await AsyncStorage.getItem(STORAGE_KEYS.USER_TOKEN);
      return token;
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  // Make API request
  async makeRequest(endpoint, options = {}) {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const token = await this.getAuthToken();

      const defaultHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (token) {
        defaultHeaders.Authorization = `Bearer ${token}`;
      }

      const config = {
        method: 'GET',
        headers: defaultHeaders,
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
      };

      console.log('Making API request to:', url);
      console.log('Request config:', JSON.stringify(config, null, 2));

      const response = await fetch(url, config);

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        const textResponse = await response.text();
        console.log('Raw response:', textResponse);
        throw new Error(`Invalid JSON response: ${textResponse.substring(0, 200)}`);
      }

      console.log('API response data:', data);

      // Normalize status field for backward compatibility
      if (data.status === 'success') {
        data.status = true;
      } else if (data.status === 'error' || data.status === 'fail') {
        data.status = false;
      }

      if (!response.ok) {
        const errorMessage = data.message || data.error || `HTTP ${response.status}: ${response.statusText}`;
        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      console.error('API request error:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        url: `${this.baseURL}${endpoint}`,
      });
      throw error;
    }
  }

  // Auth APIs
  async login(email, password) {
    return this.makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(userData) {
    return this.makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  // User APIs
  async getProfile() {
    return this.makeRequest('/user/profile');
  }

  async updateProfile(userData) {
    return this.makeRequest('/user/update-profile', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  // Dishes APIs
  async getDishes() {
    return this.makeRequest('/dishes');
  }

  async getDishDetails(dishId) {
    return this.makeRequest(`/dishes/${dishId}`);
  }

  // Cart APIs
  async getCart() {
    return this.makeRequest('/cart');
  }

  async addToCart(dishId, quantity) {
    return this.makeRequest('/cart/add', {
      method: 'POST',
      body: JSON.stringify({ dish_id: dishId, quantity }),
    });
  }

  async updateCart(dishId, quantity) {
    return this.makeRequest('/cart/update', {
      method: 'POST',
      body: JSON.stringify({ dish_id: dishId, quantity }),
    });
  }

  async removeFromCart(dishId) {
    return this.makeRequest('/cart/remove', {
      method: 'POST',
      body: JSON.stringify({ dish_id: dishId }),
    });
  }

  async clearCart() {
    return this.makeRequest('/cart/clear', {
      method: 'POST',
    });
  }

  // Booking APIs
  async placeOrder(orderData) {
    return this.makeRequest('/booking/place-order', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async getBookings() {
    return this.makeRequest('/bookings');
  }

  async getBookingDetails(bookingId) {
    return this.makeRequest(`/bookings/${bookingId}`);
  }

  async cancelBooking(bookingId) {
    return this.makeRequest(`/bookings/cancel/${bookingId}`, {
      method: 'POST',
    });
  }

  // Reviews APIs
  async addReview(reviewData) {
    return this.makeRequest('/reviews/add', {
      method: 'POST',
      body: JSON.stringify(reviewData),
    });
  }

  async getDishReviews(dishId) {
    return this.makeRequest(`/reviews/dish/${dishId}`);
  }

  // Banners APIs
  async getBanners() {
    return this.makeRequest('/banners');
  }

  // Wallet APIs
  async getWallet() {
    return this.makeRequest('/wallet');
  }

  async rechargeWallet(amount) {
    return this.makeRequest('/wallet/recharge', {
      method: 'POST',
      body: JSON.stringify({ amount }),
    });
  }

  // Settings APIs
  async getShippingSettings() {
    return this.makeRequest('/settings/shipping');
  }

  async calculateShipping(subtotal) {
    return this.makeRequest('/settings/calculate-shipping', {
      method: 'POST',
      body: JSON.stringify({ subtotal }),
    });
  }

  async getAppConfig() {
    return this.makeRequest('/settings/config');
  }
}

export default new ApiService();
