import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useCart } from '../../utils/CartContext';
import { getImageUrl } from '../../utils/textUtils';
import { calculateShippingCharge } from '../../utils/shipping';
import Header from '../../components/Header';
import { COLORS, SIZES } from '../../constants/config';

export default function CartScreen({ navigation }) {
  const {
    cartItems,
    cartLoading,
    updateCartItem,
    removeFromCart,
    clearCart,
    getCartTotal,
    getCartItemCount,
  } = useCart();

  const [shippingDetails, setShippingDetails] = useState(null);
  const [shippingLoading, setShippingLoading] = useState(true);

  const subtotal = getCartTotal();

  // Calculate shipping details when subtotal changes
  useEffect(() => {
    const fetchShippingDetails = async () => {
      if (subtotal > 0) {
        setShippingLoading(true);
        try {
          const details = await calculateShippingCharge(subtotal);
          setShippingDetails(details);
        } catch (error) {
          console.error('Error calculating shipping:', error);
          // Fallback to basic calculation
          setShippingDetails({
            subtotal: subtotal,
            shipping_charge: 30, // Default fallback
            total: subtotal + 30,
            is_free_shipping: false,
            shipping_enabled: true
          });
        } finally {
          setShippingLoading(false);
        }
      } else {
        setShippingDetails(null);
        setShippingLoading(false);
      }
    };

    fetchShippingDetails();
  }, [subtotal]);

  const handleQuantityChange = async (dishId, newQuantity) => {
    if (newQuantity <= 0) {
      Alert.alert(
        'Remove Item',
        'Are you sure you want to remove this item from cart?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Remove',
            style: 'destructive',
            onPress: () => removeFromCart(dishId)
          },
        ]
      );
    } else {
      await updateCartItem(dishId, newQuantity);
    }
  };

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: clearCart
        },
      ]
    );
  };

  const handleCheckout = () => {
    if (cartItems.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to cart before checkout');
      return;
    }
    navigation.navigate('Checkout');
  };

  const renderCartItem = ({ item }) => (
    <View style={styles.cartItem}>
      <Image
        source={{ uri: getImageUrl(item.dish_image || item.image, 'https://via.placeholder.com/80x80') }}
        style={styles.itemImage}
        resizeMode="cover"
      />

      <View style={styles.itemInfo}>
        <Text style={styles.itemName} numberOfLines={2}>{item.dish_name}</Text>
        <Text style={styles.itemPrice}>₹{item.dish_price}</Text>

        <View style={styles.quantityContainer}>
          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => handleQuantityChange(item.dish_id, item.quantity - 1)}
            disabled={cartLoading}
          >
            <Ionicons name="remove" size={16} color={COLORS.primary} />
          </TouchableOpacity>

          <Text style={styles.quantityText}>{String(item.quantity)}</Text>

          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => handleQuantityChange(item.dish_id, item.quantity + 1)}
            disabled={cartLoading}
          >
            <Ionicons name="add" size={16} color={COLORS.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.itemActions}>
        <Text style={styles.itemTotal}>
          ₹{(parseFloat(item.dish_price) * item.quantity).toFixed(2)}
        </Text>

        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => removeFromCart(item.dish_id)}
          disabled={cartLoading}
        >
          <Ionicons name="trash-outline" size={16} color={COLORS.error} />
        </TouchableOpacity>
      </View>
    </View>
  );

  if (cartItems.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="Cart" navigation={navigation} currentRoute="Cart" />
        <View style={styles.emptyContainer}>
          <Ionicons name="bag-outline" size={64} color={COLORS.textSecondary} />
          <Text style={styles.emptyTitle}>Your cart is empty</Text>
          <Text style={styles.emptySubtitle}>Add some delicious dishes to get started</Text>

          <TouchableOpacity
            style={styles.browseButton}
            onPress={() => navigation.navigate('Menu')}
          >
            <Text style={styles.browseButtonText}>Browse Menu</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header title={`Cart (${String(getCartItemCount())} items)`} navigation={navigation} currentRoute="Cart" />
      {/* Clear Cart Button */}
      <View style={styles.clearCartContainer}>
        <TouchableOpacity onPress={handleClearCart} disabled={cartLoading}>
          <Text style={styles.clearText}>Clear All</Text>
        </TouchableOpacity>
      </View>

      {/* Cart Items */}
      <FlatList
        data={cartItems}
        renderItem={renderCartItem}
        keyExtractor={(item) => item.dish_id.toString()}
        contentContainerStyle={styles.cartList}
        showsVerticalScrollIndicator={false}
      />

      {/* Cart Summary */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Subtotal</Text>
          <Text style={styles.summaryValue}>₹{subtotal.toFixed(2)}</Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Delivery Fee</Text>
          {shippingLoading ? (
            <ActivityIndicator size="small" color={COLORS.primary} />
          ) : (
            <Text style={[styles.summaryValue, shippingDetails?.is_free_shipping && styles.freeShipping]}>
              {shippingDetails?.is_free_shipping ? 'Free' : `₹${(shippingDetails?.shipping_charge || 0).toFixed(2)}`}
            </Text>
          )}
        </View>

        <View style={[styles.summaryRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Total</Text>
          {shippingLoading ? (
            <ActivityIndicator size="small" color={COLORS.primary} />
          ) : (
            <Text style={styles.totalValue}>₹{(shippingDetails?.total || subtotal).toFixed(2)}</Text>
          )}
        </View>

        <TouchableOpacity
          style={[styles.checkoutButton, cartLoading && styles.checkoutButtonDisabled]}
          onPress={handleCheckout}
          disabled={cartLoading}
        >
          {cartLoading ? (
            <ActivityIndicator color={COLORS.surface} />
          ) : (
            <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  clearCartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.padding,
    paddingVertical: SIZES.padding,
    backgroundColor: COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTitle: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  clearText: {
    fontSize: SIZES.callout,
    color: COLORS.error,
    fontWeight: '600',
  },
  cartList: {
    paddingVertical: SIZES.base,
  },
  cartItem: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    marginHorizontal: SIZES.padding,
    marginVertical: SIZES.base / 2,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: SIZES.radius / 2,
  },
  itemInfo: {
    flex: 1,
    marginLeft: SIZES.padding,
  },
  itemName: {
    fontSize: SIZES.callout,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: SIZES.body,
    color: COLORS.primary,
    fontWeight: '500',
    marginBottom: SIZES.base,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  quantityText: {
    fontSize: SIZES.body,
    fontWeight: '600',
    color: COLORS.text,
    marginHorizontal: SIZES.padding,
    minWidth: 30,
    textAlign: 'center',
  },
  itemActions: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  itemTotal: {
    fontSize: SIZES.body,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  removeButton: {
    padding: SIZES.base,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SIZES.padding,
  },
  emptyTitle: {
    fontSize: SIZES.title2,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SIZES.padding,
    marginBottom: SIZES.base,
  },
  emptySubtitle: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SIZES.padding * 2,
  },
  browseButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SIZES.padding * 2,
    paddingVertical: SIZES.padding,
    borderRadius: SIZES.radius,
  },
  browseButtonText: {
    color: COLORS.surface,
    fontSize: SIZES.body,
    fontWeight: 'bold',
  },
  summaryContainer: {
    backgroundColor: COLORS.surface,
    padding: SIZES.padding,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.base,
  },
  summaryLabel: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
  },
  summaryValue: {
    fontSize: SIZES.body,
    color: COLORS.text,
    fontWeight: '500',
  },
  freeShipping: {
    color: COLORS.success,
    fontWeight: 'bold',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SIZES.base,
    marginBottom: SIZES.padding,
  },
  totalLabel: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  totalValue: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  checkoutButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    paddingVertical: SIZES.padding,
    alignItems: 'center',
  },
  checkoutButtonDisabled: {
    opacity: 0.7,
  },
  checkoutButtonText: {
    color: COLORS.surface,
    fontSize: SIZES.body,
    fontWeight: 'bold',
  },
});
