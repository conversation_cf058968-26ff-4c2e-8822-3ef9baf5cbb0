import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import ApiService from '../../services/api';
import AuthService from '../../services/auth';
import { useCart } from '../../utils/CartContext';
import { safeText, formatPrice, formatRating, getImageUrl } from '../../utils/textUtils';
import Header from '../../components/Header';
import { COLORS, SIZES } from '../../constants/config';

export default function HomeScreen({ navigation }) {
  const [banners, setBanners] = useState([]);
  const [featuredDishes, setFeaturedDishes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [user, setUser] = useState(null);

  const { getCartItemCount } = useCart();

  useEffect(() => {
    loadHomeData();
    loadUserData();
  }, []);

  const loadUserData = () => {
    const currentUser = AuthService.getCurrentUser();
    setUser(currentUser);
  };

  const loadHomeData = async () => {
    try {
      setLoading(true);
      
      // Load banners and dishes in parallel
      const [bannersResponse, dishesResponse] = await Promise.all([
        ApiService.getBanners(),
        ApiService.getDishes(),
      ]);

      if (bannersResponse.status === true || bannersResponse.status === 'success') {
        setBanners(bannersResponse.data || []);
      }

      if (dishesResponse.status) {
        // Get first 6 dishes as featured
        setFeaturedDishes((dishesResponse.data || []).slice(0, 6));
      }
    } catch (error) {
      console.error('Error loading home data:', error);
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const renderBanner = ({ item }) => {
    const imageUrl = getImageUrl(item.image || item.image_url, 'https://via.placeholder.com/400x180/4caf50/ffffff?text=BoxBites');

    return (
      <View style={styles.bannerItem}>
        <Image
          source={{ uri: imageUrl }}
          style={styles.bannerImage}
          resizeMode="cover"
          onError={(error) => {
            console.log('Banner image load error for:', imageUrl, error.nativeEvent);
          }}
          onLoadStart={() => {
            console.log('Loading banner image:', imageUrl);
          }}
          onLoadEnd={() => {
            console.log('Banner image loaded successfully:', imageUrl);
          }}
        />
        {item.title && (
          <View style={styles.bannerOverlay}>
            <Text style={styles.bannerTitle}>{safeText(item.title, 'Banner Title')}</Text>
            {item.subtitle && (
              <Text style={styles.bannerDescription}>{safeText(item.subtitle, 'Banner Description')}</Text>
            )}
          </View>
        )}
      </View>
    );
  };

  const renderDishItem = ({ item }) => (
    <TouchableOpacity
      style={styles.dishCard}
      onPress={() => navigation.navigate('DishDetails', { dishId: item.id })}
    >
      <Image
        source={{ uri: getImageUrl(item.image || item.image_url, 'https://via.placeholder.com/150x150') }}
        style={styles.dishImage}
        resizeMode="cover"
      />
      <View style={styles.dishInfo}>
        <Text style={styles.dishName} numberOfLines={2}>{safeText(item.name, 'Dish Name')}</Text>
        <Text style={styles.dishPrice}>{formatPrice(item.price)}</Text>
        {item.rating && item.rating > 0 && (
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.warning} />
            <Text style={styles.ratingText}>{formatRating(item.rating)}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header title="BoxBites" navigation={navigation} currentRoute="Home" />
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Hello, {user?.name || 'Guest'}!</Text>
            <Text style={styles.subGreeting}>What would you like to eat today?</Text>
          </View>
          <TouchableOpacity
            style={styles.cartButton}
            onPress={() => navigation.navigate('Cart')}
          >
            <Ionicons name="bag-outline" size={24} color={COLORS.primary} />
            {getCartItemCount() > 0 && (
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>{String(getCartItemCount())}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Banners */}
        {banners.length > 0 && (
          <View style={styles.section}>
            <FlatList
              data={banners}
              renderItem={renderBanner}
              keyExtractor={(item) => item.id.toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              pagingEnabled
              style={styles.bannerList}
              onMomentumScrollEnd={(event) => {
                const index = Math.round(event.nativeEvent.contentOffset.x / (SIZES.width - SIZES.padding));
                setCurrentBannerIndex(index);
              }}
            />
            {/* Pagination Dots */}
            {banners.length > 1 && (
              <View style={styles.paginationContainer}>
                {banners.map((_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.paginationDot,
                      index === currentBannerIndex && styles.paginationDotActive
                    ]}
                  />
                ))}
              </View>
            )}
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.section}>
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Menu')}
            >
              <Ionicons name="restaurant" size={24} color={COLORS.primary} />
              <Text style={styles.actionText}>Browse Menu</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Orders')}
            >
              <Ionicons name="list" size={24} color={COLORS.primary} />
              <Text style={styles.actionText}>My Orders</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Profile')}
            >
              <Ionicons name="person" size={24} color={COLORS.primary} />
              <Text style={styles.actionText}>Profile</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Featured Dishes */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Dishes</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Menu')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={featuredDishes}
            renderItem={renderDishItem}
            keyExtractor={(item) => item.id.toString()}
            numColumns={2}
            scrollEnabled={false}
            columnWrapperStyle={styles.dishRow}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.padding,
    paddingVertical: SIZES.padding,
    backgroundColor: COLORS.surface,
  },
  greeting: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  subGreeting: {
    fontSize: SIZES.callout,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  cartButton: {
    padding: SIZES.base,
    backgroundColor: COLORS.background,
    borderRadius: SIZES.radius,
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: COLORS.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: COLORS.surface,
    fontSize: SIZES.caption2,
    fontWeight: 'bold',
  },
  section: {
    marginVertical: SIZES.base,
  },
  bannerList: {
    paddingLeft: SIZES.padding,
    paddingRight: SIZES.padding,
  },
  bannerItem: {
    width: SIZES.width - (SIZES.padding * 2), // Full width minus padding
    height: 180, // Increased height for better visibility
    marginRight: SIZES.padding,
    borderRadius: SIZES.radius,
    overflow: 'hidden',
    backgroundColor: COLORS.background,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    backgroundColor: COLORS.background,
  },
  bannerOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    padding: SIZES.padding,
    borderBottomLeftRadius: SIZES.radius,
    borderBottomRightRadius: SIZES.radius,
  },
  bannerTitle: {
    color: COLORS.surface,
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
    marginBottom: 4,
  },
  bannerDescription: {
    color: COLORS.surface,
    fontSize: SIZES.body,
    opacity: 0.9,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SIZES.padding,
    paddingHorizontal: SIZES.padding,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.border,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: COLORS.primary,
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: SIZES.padding,
  },
  actionButton: {
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    padding: SIZES.padding,
    borderRadius: SIZES.radius,
    minWidth: 80,
  },
  actionText: {
    fontSize: SIZES.caption1,
    color: COLORS.text,
    marginTop: SIZES.base / 2,
    textAlign: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.padding,
    marginBottom: SIZES.padding,
  },
  sectionTitle: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  seeAllText: {
    fontSize: SIZES.callout,
    color: COLORS.primary,
    fontWeight: '600',
  },
  dishRow: {
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.padding,
    marginBottom: SIZES.padding,
  },
  dishCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radius,
    overflow: 'hidden',
    width: '48%',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dishImage: {
    width: '100%',
    height: 120,
  },
  dishInfo: {
    padding: SIZES.padding,
  },
  dishName: {
    fontSize: SIZES.callout,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  dishPrice: {
    fontSize: SIZES.body,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
    marginLeft: 2,
  },
});
