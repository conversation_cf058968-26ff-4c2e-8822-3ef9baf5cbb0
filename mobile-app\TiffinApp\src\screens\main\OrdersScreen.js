import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import ApiService from '../../services/api';
import Header from '../../components/Header';
import { COLORS, SIZES, ORDER_STATUS } from '../../constants/config';

export default function OrdersScreen({ navigation }) {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getBookings();

      if (response.status && response.data) {
        setOrders(response.data);
      } else {
        console.log('No orders found or error:', response.message);
        setOrders([]);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
      Alert.alert('Error', 'Failed to load orders. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case ORDER_STATUS.PENDING:
        return COLORS.warning;
      case ORDER_STATUS.CONFIRMED:
        return COLORS.info;
      case ORDER_STATUS.PREPARING:
        return COLORS.secondary;
      case ORDER_STATUS.OUT_FOR_DELIVERY:
        return COLORS.primary;
      case ORDER_STATUS.DELIVERED:
        return COLORS.success;
      case ORDER_STATUS.CANCELLED:
        return COLORS.error;
      default:
        return COLORS.textSecondary;
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case ORDER_STATUS.PENDING:
        return 'time-outline';
      case ORDER_STATUS.CONFIRMED:
        return 'checkmark-circle-outline';
      case ORDER_STATUS.PREPARING:
        return 'restaurant-outline';
      case ORDER_STATUS.OUT_FOR_DELIVERY:
        return 'bicycle-outline';
      case ORDER_STATUS.DELIVERED:
        return 'checkmark-done-outline';
      case ORDER_STATUS.CANCELLED:
        return 'close-circle-outline';
      default:
        return 'help-circle-outline';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleCancelOrder = (orderId, order) => {
    const paymentMethod = order?.payment_method?.toLowerCase() || 'cod';

    let message = 'Are you sure you want to cancel this order?';

    if (paymentMethod === 'wallet' || paymentMethod === 'razorpay') {
      message += '\n\nThe amount will be refunded to your wallet.';
    } else if (paymentMethod === 'cod') {
      message += '\n\nThis is a COD order, no refund will be processed.';
    }

    Alert.alert(
      'Cancel Order',
      message,
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: () => cancelOrder(orderId)
        },
      ]
    );
  };

  const cancelOrder = async (orderId) => {
    try {
      const response = await ApiService.cancelBooking(orderId);

      if (response.status) {
        // Show appropriate success message based on response
        const message = response.message || 'Order cancelled successfully';
        Alert.alert('Success', message);
        loadOrders(); // Refresh orders
      } else {
        Alert.alert('Error', response.message || 'Failed to cancel order');
      }
    } catch (error) {
      console.error('Error cancelling order:', error);
      Alert.alert('Error', 'Failed to cancel order. Please try again.');
    }
  };

  const renderOrderItem = ({ item }) => {
    const canCancel = item.status?.toLowerCase() === ORDER_STATUS.PENDING ||
                     item.status?.toLowerCase() === ORDER_STATUS.CONFIRMED;

    return (
      <TouchableOpacity
        style={styles.orderCard}
        onPress={() => navigation.navigate('OrderDetails', { orderId: item.id, order: item })}
      >
        <View style={styles.orderHeader}>
          <View style={styles.orderInfo}>
            <Text style={styles.orderId}>Order #{String(item.id)}</Text>
            <Text style={styles.orderDate}>{formatDate(item.created_at)}</Text>
          </View>

          <View style={[styles.statusContainer, { backgroundColor: getStatusColor(item.status) + '20' }]}>
            <Ionicons
              name={getStatusIcon(item.status)}
              size={16}
              color={getStatusColor(item.status)}
            />
            <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
              {item.status?.replace('_', ' ').toUpperCase() || 'UNKNOWN'}
            </Text>
          </View>
        </View>

        <View style={styles.orderDetails}>
          <Text style={styles.orderItems} numberOfLines={2}>
            {item.items ?
              item.items.map(orderItem => `${orderItem.quantity}x ${orderItem.dish_name}`).join(', ') :
              'Order details'
            }
          </Text>

          <View style={styles.paymentMethodContainer}>
            <Text style={styles.paymentMethodLabel}>Payment: </Text>
            <Text style={styles.paymentMethodText}>
              {item.payment_method ? item.payment_method.toUpperCase() : 'COD'}
            </Text>
          </View>

          <View style={styles.orderFooter}>
            <Text style={styles.orderTotal}>₹{parseFloat(item.total_amount || 0).toFixed(2)}</Text>

            {canCancel && (
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => handleCancelOrder(item.id, item)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="My Orders" navigation={navigation} currentRoute="Orders" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading orders...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header title="My Orders" navigation={navigation} currentRoute="Orders" />
      <FlatList
        data={orders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.ordersList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="receipt-outline" size={64} color={COLORS.textSecondary} />
            <Text style={styles.emptyTitle}>No orders yet</Text>
            <Text style={styles.emptySubtitle}>
              Your order history will appear here
            </Text>

            <TouchableOpacity
              style={styles.browseButton}
              onPress={() => navigation.navigate('Menu')}
            >
              <Text style={styles.browseButtonText}>Browse Menu</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
    marginTop: SIZES.padding,
  },
  ordersList: {
    padding: SIZES.padding,
  },
  orderCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    marginBottom: SIZES.padding,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SIZES.padding,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: SIZES.body,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 2,
  },
  orderDate: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SIZES.base,
    paddingVertical: SIZES.base / 2,
    borderRadius: SIZES.radius / 2,
  },
  statusText: {
    fontSize: SIZES.caption1,
    fontWeight: '600',
    marginLeft: 4,
  },
  orderDetails: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SIZES.padding,
  },
  orderItems: {
    fontSize: SIZES.callout,
    color: COLORS.text,
    marginBottom: SIZES.padding,
    lineHeight: 20,
  },
  paymentMethodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.base,
  },
  paymentMethodLabel: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
  },
  paymentMethodText: {
    fontSize: SIZES.caption1,
    color: COLORS.primary,
    fontWeight: '600',
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderTotal: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  cancelButton: {
    backgroundColor: COLORS.error + '20',
    paddingHorizontal: SIZES.padding,
    paddingVertical: SIZES.base,
    borderRadius: SIZES.radius / 2,
    borderWidth: 1,
    borderColor: COLORS.error,
  },
  cancelButtonText: {
    color: COLORS.error,
    fontSize: SIZES.caption1,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SIZES.padding * 4,
  },
  emptyTitle: {
    fontSize: SIZES.title2,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SIZES.padding,
    marginBottom: SIZES.base,
  },
  emptySubtitle: {
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SIZES.padding * 2,
  },
  browseButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SIZES.padding * 2,
    paddingVertical: SIZES.padding,
    borderRadius: SIZES.radius,
  },
  browseButtonText: {
    color: COLORS.surface,
    fontSize: SIZES.body,
    fontWeight: 'bold',
  },
});
