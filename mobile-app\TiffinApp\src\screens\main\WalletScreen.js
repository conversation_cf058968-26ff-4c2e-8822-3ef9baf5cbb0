import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import ApiService from '../../services/api';
import Header from '../../components/Header';
import { COLORS, SIZES } from '../../constants/config';

export default function WalletScreen({ navigation }) {
  const [walletBalance, setWalletBalance] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [rechargeAmount, setRechargeAmount] = useState('');
  const [recharging, setRecharging] = useState(false);

  useEffect(() => {
    fetchWalletData();
  }, []);

  const fetchWalletData = async () => {
    try {
      const response = await ApiService.getWallet();
      if (response.status) {
        // Ensure balance is always a number
        const balance = parseFloat(response.data.balance) || 0;
        setWalletBalance(balance);
        setTransactions(response.data.transactions);
      }
    } catch (error) {
      console.error('Error fetching wallet data:', error);
      Alert.alert('Error', 'Failed to load wallet data');
      setWalletBalance(0); // Set to 0 on error
    } finally {
      setLoading(false);
    }
  };

  const handleRecharge = async () => {
    const amount = parseFloat(rechargeAmount);
    
    if (!amount || amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (amount < 10) {
      Alert.alert('Error', 'Minimum recharge amount is ₹10');
      return;
    }

    if (amount > 10000) {
      Alert.alert('Error', 'Maximum recharge amount is ₹10,000');
      return;
    }

    Alert.alert(
      'Recharge Wallet',
      `Recharge ₹${amount.toFixed(2)} to your wallet?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Recharge', onPress: () => processRecharge(amount) }
      ]
    );
  };

  const processRecharge = async (amount) => {
    setRecharging(true);
    try {
      const response = await ApiService.rechargeWallet(amount);
      if (response.status) {
        setWalletBalance(prev => prev + amount);
        setRechargeAmount('');
        Alert.alert('Success', `₹${amount.toFixed(2)} has been added to your wallet`);
        fetchWalletData(); // Refresh data
      } else {
        Alert.alert('Error', response.message || 'Failed to recharge wallet');
      }
    } catch (error) {
      console.error('Error recharging wallet:', error);
      Alert.alert('Error', 'Failed to recharge wallet. Please try again.');
    } finally {
      setRecharging(false);
    }
  };

  const renderTransaction = ({ item }) => (
    <View style={styles.transactionItem}>
      <View style={styles.transactionIcon}>
        <Ionicons 
          name={item.type === 'credit' ? 'add-circle' : 'remove-circle'} 
          size={24} 
          color={item.type === 'credit' ? COLORS.success : COLORS.error} 
        />
      </View>
      <View style={styles.transactionDetails}>
        <Text style={styles.transactionDescription}>{item.description}</Text>
        <Text style={styles.transactionDate}>{item.date}</Text>
      </View>
      <Text style={[
        styles.transactionAmount,
        { color: item.type === 'credit' ? COLORS.success : COLORS.error }
      ]}>
        {item.type === 'credit' ? '+' : '-'}₹{parseFloat(item.amount).toFixed(2)}
      </Text>
    </View>
  );

  const quickAmounts = [100, 200, 500, 1000];

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="Wallet" navigation={navigation} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading wallet...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header title="Wallet" navigation={navigation} />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Wallet Balance Card */}
        <View style={styles.balanceCard}>
          <View style={styles.balanceHeader}>
            <Ionicons name="wallet" size={32} color={COLORS.primary} />
            <Text style={styles.balanceTitle}>Wallet Balance</Text>
          </View>
          <Text style={styles.balanceAmount}>₹{(parseFloat(walletBalance) || 0).toFixed(2)}</Text>
        </View>

        {/* Recharge Section */}
        <View style={styles.rechargeSection}>
          <Text style={styles.sectionTitle}>Recharge Wallet</Text>
          
          <View style={styles.amountInput}>
            <Text style={styles.inputLabel}>Enter Amount</Text>
            <TextInput
              style={styles.textInput}
              value={rechargeAmount}
              onChangeText={setRechargeAmount}
              placeholder="Enter amount (₹10 - ₹10,000)"
              keyboardType="numeric"
              editable={!recharging}
            />
          </View>

          <View style={styles.quickAmounts}>
            <Text style={styles.quickAmountsLabel}>Quick Amounts:</Text>
            <View style={styles.quickAmountsRow}>
              {quickAmounts.map((amount) => (
                <TouchableOpacity
                  key={amount}
                  style={styles.quickAmountButton}
                  onPress={() => setRechargeAmount(amount.toString())}
                  disabled={recharging}
                >
                  <Text style={styles.quickAmountText}>₹{amount}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <TouchableOpacity
            style={[styles.rechargeButton, recharging && styles.disabledButton]}
            onPress={handleRecharge}
            disabled={recharging}
          >
            {recharging ? (
              <ActivityIndicator color={COLORS.surface} />
            ) : (
              <Text style={styles.rechargeButtonText}>Recharge Now</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Transaction History */}
        <View style={styles.transactionsSection}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          {transactions.length > 0 ? (
            <FlatList
              data={transactions}
              renderItem={renderTransaction}
              keyExtractor={(item) => item.id.toString()}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyTransactions}>
              <Ionicons name="receipt-outline" size={48} color={COLORS.textLight} />
              <Text style={styles.emptyTransactionsText}>No transactions yet</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SIZES.padding,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: SIZES.base,
    fontSize: SIZES.body,
    color: COLORS.textSecondary,
  },
  balanceCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radius,
    padding: SIZES.padding * 1.5,
    marginBottom: SIZES.padding,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  balanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.padding,
  },
  balanceTitle: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
    marginLeft: SIZES.base,
  },
  balanceAmount: {
    fontSize: SIZES.largeTitle,
    fontWeight: 'bold',
    color: COLORS.primary,
    textAlign: 'center',
  },
  rechargeSection: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    marginBottom: SIZES.padding,
  },
  sectionTitle: {
    fontSize: SIZES.title3,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SIZES.padding,
  },
  amountInput: {
    marginBottom: SIZES.padding,
  },
  inputLabel: {
    fontSize: SIZES.body,
    color: COLORS.text,
    marginBottom: SIZES.base,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    fontSize: SIZES.body,
    color: COLORS.text,
  },
  quickAmounts: {
    marginBottom: SIZES.padding,
  },
  quickAmountsLabel: {
    fontSize: SIZES.body,
    color: COLORS.text,
    marginBottom: SIZES.base,
  },
  quickAmountsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAmountButton: {
    backgroundColor: COLORS.background,
    borderRadius: SIZES.radius,
    paddingVertical: SIZES.base,
    paddingHorizontal: SIZES.padding,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  quickAmountText: {
    fontSize: SIZES.body,
    color: COLORS.text,
    fontWeight: '500',
  },
  rechargeButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    paddingVertical: SIZES.padding,
    alignItems: 'center',
  },
  rechargeButtonText: {
    color: COLORS.surface,
    fontSize: SIZES.body,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  transactionsSection: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SIZES.padding,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  transactionIcon: {
    marginRight: SIZES.padding,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: SIZES.body,
    color: COLORS.text,
    fontWeight: '500',
  },
  transactionDate: {
    fontSize: SIZES.caption1,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: SIZES.body,
    fontWeight: 'bold',
  },
  emptyTransactions: {
    alignItems: 'center',
    paddingVertical: SIZES.padding * 2,
  },
  emptyTransactionsText: {
    fontSize: SIZES.body,
    color: COLORS.textLight,
    marginTop: SIZES.base,
  },
});
