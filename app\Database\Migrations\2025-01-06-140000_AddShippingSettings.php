<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddShippingSettings extends Migration
{
    public function up()
    {
        // Add shipping-related settings
        $data = [
            [
                'setting_key' => 'shipping_enabled',
                'setting_value' => '1',
                'setting_type' => 'boolean',
                'description' => 'Enable or disable shipping charges',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'shipping_charge',
                'setting_value' => '50',
                'setting_type' => 'number',
                'description' => 'Fixed shipping charge amount',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'free_shipping_threshold',
                'setting_value' => '500',
                'setting_type' => 'number',
                'description' => 'Minimum order amount for free shipping',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'shipping_message',
                'setting_value' => 'Free shipping on orders above ₹500',
                'setting_type' => 'text',
                'description' => 'Shipping message displayed to customers',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Check if settings already exist before inserting
        foreach ($data as $setting) {
            $existing = $this->db->table('settings')
                ->where('setting_key', $setting['setting_key'])
                ->get()
                ->getRow();
            
            if (!$existing) {
                $this->db->table('settings')->insert($setting);
            }
        }
    }

    public function down()
    {
        // Remove the added settings
        $keys = [
            'shipping_enabled',
            'shipping_charge', 
            'free_shipping_threshold',
            'shipping_message'
        ];

        foreach ($keys as $key) {
            $this->db->table('settings')->where('setting_key', $key)->delete();
        }
    }
}
