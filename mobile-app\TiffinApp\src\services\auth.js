import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants/config';
import ApiService from './api';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.isAuthenticated = false;
  }

  // Login user
  async login(email, password) {
    try {
      const response = await ApiService.login(email, password);

      console.log('Auth service received response:', response);

      if (response.status && response.data && response.data.token) {
        // Store token and user data
        await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, response.data.token);
        await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(response.data.user));

        this.currentUser = response.data.user;
        this.isAuthenticated = true;

        return {
          success: true,
          user: response.data.user,
          token: response.data.token,
        };
      } else {
        throw new Error(response.message || 'Lo<PERSON> failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.message || 'Login failed',
      };
    }
  }

  // Register user
  async register(userData) {
    try {
      const response = await ApiService.register(userData);

      console.log('Auth service received registration response:', response);

      if (response.status && response.data && response.data.token) {
        // Store token and user data
        await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, response.data.token);
        await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(response.data.user));

        this.currentUser = response.data.user;
        this.isAuthenticated = true;

        return {
          success: true,
          user: response.data.user,
          token: response.data.token,
        };
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message || 'Registration failed',
      };
    }
  }

  // Logout user
  async logout() {
    try {
      // Clear stored data
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_TOKEN);
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
      await AsyncStorage.removeItem(STORAGE_KEYS.CART_DATA);
      
      this.currentUser = null;
      this.isAuthenticated = false;
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        error: error.message || 'Logout failed',
      };
    }
  }

  // Check if user is authenticated
  async checkAuthStatus() {
    try {
      const token = await AsyncStorage.getItem(STORAGE_KEYS.USER_TOKEN);
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      
      if (token && userData) {
        this.currentUser = JSON.parse(userData);
        this.isAuthenticated = true;
        return {
          isAuthenticated: true,
          user: this.currentUser,
          token,
        };
      } else {
        this.currentUser = null;
        this.isAuthenticated = false;
        return {
          isAuthenticated: false,
          user: null,
          token: null,
        };
      }
    } catch (error) {
      console.error('Auth status check error:', error);
      this.currentUser = null;
      this.isAuthenticated = false;
      return {
        isAuthenticated: false,
        user: null,
        token: null,
      };
    }
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Check if authenticated
  getIsAuthenticated() {
    return this.isAuthenticated;
  }

  // Update user profile
  async updateProfile(userData) {
    try {
      const response = await ApiService.updateProfile(userData);
      
      if (response.status) {
        // Update stored user data
        const updatedUser = { ...this.currentUser, ...userData };
        await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(updatedUser));
        this.currentUser = updatedUser;
        
        return {
          success: true,
          user: updatedUser,
        };
      } else {
        throw new Error(response.message || 'Profile update failed');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      return {
        success: false,
        error: error.message || 'Profile update failed',
      };
    }
  }

  // Get user token
  async getToken() {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.USER_TOKEN);
    } catch (error) {
      console.error('Get token error:', error);
      return null;
    }
  }
}

export default new AuthService();
